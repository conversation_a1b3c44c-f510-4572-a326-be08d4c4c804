"use client";

import { motion } from "framer-motion";
import {
  ArrowRight,
  Bot,
  Shield,
  Zap,
  BarChart3,
  Target,
  Smartphone,
  Monitor,
  Activity,
  DollarSign,
  Settings,
  MouseIcon,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import EmailVerificationModal from "@/components/EmailVerificationModal";
import { useState } from "react";

const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6, ease: "easeOut" },
};

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1,
    },
  },
};

// const stats = [
//   { label: "Active Traders", value: "10,000+", icon: Users },
//   { label: "Total Volume", value: "$50M+", icon: TrendingUp },
//   { label: "Success Rate", value: "94.2%", icon: Star },
// ];

const features = [
  {
    icon: Bot,
    title: "Trading Automation",
    description:
      "Advanced algorithms that analyze market patterns and execute trades with precision, maximizing your profits while you sleep.",
    gradient: "from-blue-500 to-purple-600",
  },
  {
    icon: Shield,
    title: "Decentralized Security",
    description:
      "Your funds remain in your wallet. No custodial risks, no centralized points of failure. Trade with complete peace of mind.",
    gradient: "from-purple-500 to-pink-600",
  },
  {
    icon: Zap,
    title: "Lightning Fast Execution",
    description:
      "Execute trades in milliseconds on Solana&apos;s high-performance blockchain. Never miss an opportunity again.",
    gradient: "from-pink-500 to-orange-600",
  },
];

const appFeatures = [
  {
    icon: Target,
    title: "Smart Indicator Bot",
    description:
      "Intuitive bot setup with customizable strategies, risk management, and automated execution.",
    features: [
      "Strategy templates",
      "Risk controls",
      "Backtesting",
      "Performance optimization",
    ],
    image: "/screen-2.png",
    gradient: "from-blue-500 to-cyan-500",
  },
  {
    icon: Bot,
    title: "AI-Powered Coin Sniper",
    description:
      "Real-time market analysis with comprehensive charts, indicators, and performance metrics.",
    features: [
      "Live price charts",
      "Technical indicators",
      "Portfolio tracking",
      "P&L analytics",
    ],
    image: "/screen-1.png",
    gradient: "from-purple-500 to-blue-500",
  },
  {
    icon: Activity,
    title: "Real-Time Trading Feed",
    description:
      "Live transaction monitoring with detailed trade history and execution analytics.",
    features: [
      "Live trade feed",
      "Transaction history",
      "Execution analytics",
      "Performance metrics",
    ],
    image: "/screen-3.png",
    gradient: "from-pink-500 to-purple-500",
  },
  {
    icon: DollarSign,
    title: "Token Discovery",
    description:
      "Discover new tokens on Solana with real-time data from Pump.fun, Raydium, and DexScreener.",
    features: [
      "New token alerts",
      "Market cap tracking",
      "Volume analysis",
      "Social sentiment",
    ],
    image: "/screen-4.png",
    gradient: "from-orange-500 to-red-500",
  },
  {
    icon: Settings,
    title: "Portfolio Management",
    description:
      "Comprehensive portfolio tracking with detailed analytics and performance insights.",
    features: [
      "Asset allocation",
      "Performance tracking",
      "Risk analysis",
      "Rebalancing tools",
    ],
    image: "/screen-5.png",
    gradient: "from-green-500 to-emerald-500",
  },
  // {
  //   icon: Eye,
  //   title: "Market Intelligence",
  //   description:
  //     "Advanced market analysis with AI-powered insights and trend detection.",
  //   features: [
  //     "Market trends",
  //     "AI insights",
  //     "Sentiment analysis",
  //     "Price predictions",
  //   ],
  //   image: "/screenshots/market-intel.svg",
  //   gradient: "from-indigo-500 to-purple-500",
  // },
];

export default function LandingPage() {
  const [isEmailVerificationOpen, setIsEmailVerificationOpen] = useState(false);

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-background/80">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-solid/5 via-transparent to-primary-solid/10" />
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-32">
          <motion.div
            initial="initial"
            animate="animate"
            variants={staggerContainer}
            className="text-center"
          >
            <motion.div variants={fadeInUp} className="mb-8">
              <Badge
                variant="secondary"
                className="hidden mx-auto md:flex mb-4 px-4 py-2 text-sm font-medium bg-primary-solid/10 text-primary-solid border-primary-solid/20 "
              >
                🚀 World&apos;s First Decentralized Indicator-Based Trading
              </Badge>
            </motion.div>

            <motion.h1
              variants={fadeInUp}
              className="text-4xl sm:text-6xl lg:text-7xl font-bold tracking-tight mb-8"
            >
              <span className="block text-foreground">Trade Strong with</span>
              <span className=" bg-primary-gradient bg-clip-text text-transparent">
                DexTrip Intelligence
              </span>
            </motion.h1>

            <motion.p
              variants={fadeInUp}
              className="text-xl sm:text-2xl text-muted-foreground max-w-3xl mx-auto mb-24 leading-relaxed"
            >
              The most powerful decentralized trading platform on Solana.
              AI-driven bots, advanced indicators, and complete control over
              your assets.
            </motion.p>

            <motion.div
              variants={fadeInUp}
              animate={{
                y: [0, 50, 0], // Animate from 0 to 50, then back to 0
              }}
              initial={{ opacity: 0, y: 60 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{
                duration: 2, // Animation duration of 2 seconds
                repeat: Infinity, // Repeat the animation infinitely
                repeatType: "reverse", // Reverse the animation on each repeat
                ease: "easeInOut", // Use an easing function for smooth animation
              }}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center my-16"
            >
              <MouseIcon className="w-16 h-16" />
            </motion.div>

            {/* Stats */}
            {/* <motion.div
              variants={fadeInUp}
              className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-4xl mx-auto">
              {stats.map((stat) => (
                <motion.div
                  key={stat.label}
                  variants={fadeInUp}
                  className="text-center">
                  <div className="flex justify-center mb-2">
                    <stat.icon className="h-6 w-6 text-primary-solid" />
                  </div>
                  <div className="text-3xl font-bold text-foreground mb-1">
                    {stat.value}
                  </div>
                  <div className="text-muted-foreground">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div> */}
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-primary-solid/5 to-transparent" />
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="text-center mb-16"
          >
            <motion.h2
              variants={fadeInUp}
              className="text-3xl sm:text-5xl font-bold mb-6"
            >
              The Power in Your Hands
            </motion.h2>
            <motion.p
              variants={fadeInUp}
              className="text-xl text-muted-foreground max-w-3xl mx-auto"
            >
              DexTrip is your sword and shield in the decentralized trading
              arena
            </motion.p>
          </motion.div>

          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-3 gap-8"
          >
            {features.map((feature) => (
              <motion.div key={feature.title} variants={fadeInUp}>
                <Card className="h-full bg-card/50 backdrop-blur-sm border-border/50 hover:border-primary-solid/30 transition-all duration-300 group hover:shadow-2xl hover:shadow-primary-solid/10">
                  <CardContent className="p-8">
                    <div
                      className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${feature.gradient} p-4 mb-6 group-hover:scale-110 transition-transform duration-300`}
                    >
                      <feature.icon className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold mb-4 text-foreground">
                      {feature.title}
                    </h3>
                    <p className="text-muted-foreground leading-relaxed">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* App Features Showcase */}
      <section className="py-24 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-primary-solid/5 via-transparent to-primary-solid/5" />
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="text-center mb-16"
          >
            <motion.h2
              variants={fadeInUp}
              className="text-3xl sm:text-5xl font-bold mb-6"
            >
              Experience the Platform
            </motion.h2>
            <motion.p
              variants={fadeInUp}
              className="text-xl text-muted-foreground max-w-3xl mx-auto"
            >
              Explore DexTrip&apos;s comprehensive suite of trading tools and
              analytics
            </motion.p>
          </motion.div>

          <div className="space-y-24">
            {appFeatures.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial="initial"
                whileInView="animate"
                viewport={{ once: true }}
                variants={staggerContainer}
                className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${
                  index % 2 === 1 ? "lg:grid-flow-col-dense" : ""
                }`}
              >
                {/* Feature Content */}
                <motion.div
                  variants={fadeInUp}
                  className={`space-y-6 ${
                    index % 2 === 1 ? "lg:col-start-2" : ""
                  }`}
                >
                  <div
                    className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${feature.gradient} p-4 mb-6`}
                  >
                    <feature.icon className="w-8 h-8 text-white" />
                  </div>

                  <h3 className="text-3xl font-bold text-foreground">
                    {feature.title}
                  </h3>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    {feature.description}
                  </p>

                  <div className="grid grid-cols-2 gap-3">
                    {feature.features.map((item) => (
                      <div key={item} className="flex items-center space-x-2">
                        <div
                          className={`w-2 h-2 rounded-full bg-gradient-to-r ${feature.gradient}`}
                        />
                        <span className="text-sm text-foreground">{item}</span>
                      </div>
                    ))}
                  </div>

                  {/* <Button
                    variant="outline"
                    className="mt-6 border-2 hover:bg-accent/50">
                    Learn More
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button> */}
                </motion.div>

                {/* Feature Screenshot */}
                <motion.div
                  variants={fadeInUp}
                  className={`relative ${
                    index % 2 === 1 ? "lg:col-start-1 lg:row-start-1" : ""
                  }`}
                >
                  <div className="relative bg-gradient-to-br from-card via-card/80 to-card/60 backdrop-blur-sm rounded-3xl p-4 border border-border/50 shadow-2xl">
                    <div
                      className={`absolute inset-0 bg-gradient-to-br ${feature.gradient}/5 rounded-3xl`}
                    />

                    {/* Screenshot Display */}
                    <div className="relative bg-background/50 rounded-2xl overflow-hidden border border-border/30">
                      <Image
                        src={feature.image}
                        alt={`${feature.title} Screenshot`}
                        width={800}
                        height={600}
                        className="w-full h-auto"
                        priority={index < 2}
                      />
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Key Features Grid */}
      <section className="py-24 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-primary-solid/5 to-transparent" />
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="text-center mb-16"
          >
            <motion.h2
              variants={fadeInUp}
              className="text-3xl sm:text-5xl font-bold mb-6"
            >
              Why Choose DexTrip?
            </motion.h2>
            <motion.p
              variants={fadeInUp}
              className="text-xl text-muted-foreground max-w-3xl mx-auto"
            >
              The most comprehensive decentralized trading platform with
              cutting-edge features
            </motion.p>
          </motion.div>

          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {[
              {
                icon: Bot,
                title: "Automated Trading",
                desc: "Set it and forget it with AI-powered bots",
                color: "from-blue-500 to-cyan-500",
              },
              {
                icon: Shield,
                title: "Non-Custodial",
                desc: "Your keys, your crypto, your control",
                color: "from-green-500 to-emerald-500",
              },
              {
                icon: Zap,
                title: "Lightning Fast",
                desc: "Execute trades in milliseconds on Solana",
                color: "from-yellow-500 to-orange-500",
              },
              {
                icon: BarChart3,
                title: "Advanced Analytics",
                desc: "Professional-grade charts and indicators",
                color: "from-purple-500 to-pink-500",
              },
              {
                icon: Target,
                title: "Smart Strategies",
                desc: "Pre-built and custom trading strategies",
                color: "from-indigo-500 to-purple-500",
              },
              {
                icon: DollarSign,
                title: "Low Fees",
                desc: "Minimal fees on the fastest blockchain",
                color: "from-red-500 to-pink-500",
              },
            ].map((item) => (
              <motion.div
                key={item.title}
                variants={fadeInUp}
                className="group"
              >
                <Card className="h-full bg-card/50 backdrop-blur-sm border-border/50 hover:border-primary-solid/30 transition-all duration-300 group-hover:shadow-2xl group-hover:shadow-primary-solid/10">
                  <CardContent className="p-6 text-center">
                    <div
                      className={`w-16 h-16 mx-auto rounded-2xl bg-gradient-to-br ${item.color} p-4 mb-4 group-hover:scale-110 transition-transform duration-300`}
                    >
                      <item.icon className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-xl font-bold mb-2 text-foreground">
                      {item.title}
                    </h3>
                    <p className="text-muted-foreground text-sm">{item.desc}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Revolution Section */}
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-primary-gradient" />
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -60 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
            >
              {/* <Badge
                variant="secondary"
                className="mb-6 px-4 py-2 bg-primary-solid/10 text-primary-solid border-primary-solid/20">
                Independence in your hands
              </Badge> */}

              <h2 className="text-3xl sm:text-5xl font-bold mb-6 text-white">
                A Revolution in Financial Freedom
              </h2>

              <p className="text-xl text-white/80 mb-8 leading-relaxed">
                Solana is the fastest-growing blockchain ecosystem in DeFi and
                Web3. DexTrip harnesses this power to give you unprecedented
                control over your trading destiny.
              </p>

              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full" />
                  <span className="text-white">
                    Lightning-fast transactions on Solana
                  </span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full" />
                  <span className="text-white">Near-zero trading fees</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-gradient-to-r from-pink-500 to-orange-600 rounded-full" />
                  <span className="text-white">
                    Complete custody of your assets
                  </span>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 60 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="relative"
            >
              <div className="relative bg-gradient-to-br from-card via-card/80 to-card/60 backdrop-blur-sm rounded-3xl p-8 border border-border/50">
                <div className="absolute inset-0 bg-gradient-to-br from-primary-solid/5 to-transparent rounded-3xl" />
                <div className="relative">
                  <h3 className="text-2xl font-bold mb-6 text-white">
                    Keys to the New World
                  </h3>
                  <p className="text-muted-foreground mb-6 leading-relaxed">
                    Your DexTrip account is your all-access pass to
                    decentralized trading. It&apos;s a secure way to deploy
                    bots, analyze markets, and explore the future of finance.
                  </p>
                  <div className="text-sm text-primary-solid font-medium">
                    The power is in your hands.
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Platform Showcase */}
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-pink-500/5" />
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="text-center mb-16"
          >
            <motion.h2
              variants={fadeInUp}
              className="text-3xl sm:text-5xl font-bold mb-6"
            >
              Trade Anywhere, Anytime
            </motion.h2>
            <motion.p
              variants={fadeInUp}
              className="text-xl text-muted-foreground max-w-3xl mx-auto"
            >
              Access DexTrip on any device with our responsive web platform and
              upcoming mobile apps
            </motion.p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
            <motion.div
              initial={{ opacity: 0, y: 60 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-center"
            >
              <div className="relative mb-6">
                <div className="w-24 h-24 mx-auto rounded-3xl bg-gradient-to-br from-blue-500 to-purple-600 p-6 shadow-2xl">
                  <Monitor className="w-12 h-12 text-white" />
                </div>
              </div>

              <h3 className="text-2xl font-bold mb-4 text-foreground">
                Desktop Web
              </h3>
              <p className="text-muted-foreground mb-4">
                Full-featured trading platform with advanced analytics and
                comprehensive bot management
              </p>
              {/* <Badge
                variant="secondary"
                className="bg-green-500/10 text-green-500 border-green-500/20">
                Available Now
              </Badge> */}
              <Badge
                variant="secondary"
                className="bg-orange-500/10 text-orange-500 border-orange-500/20"
              >
                Beta Access
              </Badge>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 60 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-center"
            >
              <div className="relative mb-6">
                <div className="w-24 h-24 mx-auto rounded-3xl bg-gradient-to-br from-purple-500 to-pink-600 p-6 shadow-2xl">
                  <Smartphone className="w-12 h-12 text-white" />
                </div>
              </div>

              <h3 className="text-2xl font-bold mb-4 text-foreground">
                Mobile App
              </h3>
              <p className="text-muted-foreground mb-4">
                Trade on the go with our mobile-optimized interface and push
                notifications
              </p>
              <Badge
                variant="secondary"
                className="bg-blue-500/10 text-blue-500 border-blue-500/20"
              >
                Coming Soon
              </Badge>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 60 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="text-center"
            >
              <div className="relative mb-6">
                <div className="w-24 h-24 mx-auto rounded-3xl bg-gradient-to-br from-pink-500 to-orange-600 p-6 shadow-2xl">
                  <Settings className="w-12 h-12 text-white" />
                </div>
              </div>

              <h3 className="text-2xl font-bold mb-4 text-foreground">
                Developer API
              </h3>
              <p className="text-muted-foreground mb-4">
                Build custom integrations with our comprehensive REST API and
                WebSocket feeds
              </p>
              {/* <Badge
                variant="secondary"
                className="bg-orange-500/10 text-orange-500 border-orange-500/20">
                Beta Access
              </Badge> */}
              <Badge
                variant="secondary"
                className="bg-blue-500/10 text-blue-500 border-blue-500/20"
              >
                Coming Soon
              </Badge>
            </motion.div>
          </div>

          {/* Stats Grid */}
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="grid grid-cols-2 lg:grid-cols-4 gap-8 text-center"
          >
            <motion.div variants={fadeInUp} className="space-y-2">
              <div className="text-3xl font-bold text-foreground">99.9%</div>
              <div className="text-muted-foreground">Uptime</div>
            </motion.div>

            <motion.div variants={fadeInUp} className="space-y-2">
              <div className="text-3xl font-bold text-foreground">&lt;50ms</div>
              <div className="text-muted-foreground">Latency</div>
            </motion.div>

            <motion.div variants={fadeInUp} className="space-y-2">
              <div className="text-3xl font-bold text-foreground">24/7</div>
              <div className="text-muted-foreground">Support</div>
            </motion.div>

            <motion.div variants={fadeInUp} className="space-y-2">
              <div className="text-3xl font-bold text-foreground">100+</div>
              <div className="text-muted-foreground">Indicators</div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary-solid/10 via-transparent to-primary-solid/5" />
        <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerContainer}
          >
            <motion.h2
              variants={fadeInUp}
              className="text-3xl sm:text-5xl font-bold mb-6"
            >
              In Every Wallet a Kingdom,
              <br />
              <span className="bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent">
                On Every Head a Crown
              </span>
            </motion.h2>

            <motion.p
              variants={fadeInUp}
              className="text-xl text-muted-foreground mb-12 max-w-2xl mx-auto leading-relaxed"
            >
              Master your own crypto destiny with DexTrip&apos;s advanced
              trading intelligence.
            </motion.p>

            <motion.div variants={fadeInUp} className="space-y-6">
              {/* <div className="text-center mb-8">
                <h3 className="text-2xl font-bold mb-4 text-foreground">
                  DexTrip: The Most Powerful Trading Platform on Solana
                </h3>
              </div> */}

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button
                  onClick={() => setIsEmailVerificationOpen(true)}
                  size="lg"
                  className="bg-primary-gradient text-white px-12 py-4 font-semibold shadow-2xl hover:shadow-purple-500/25 transition-all duration-300 group"
                >
                  Join Waitlist
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </Button>
                <EmailVerificationModal
                  isOpen={isEmailVerificationOpen}
                  onClose={() => setIsEmailVerificationOpen(false)}
                />
                {/* <Link href="/dashboard">
                  <Button
                    variant="outline"
                    size="lg"
                    className="px-12 py-4 text-lg font-semibold border-2 hover:bg-accent/50">
                    Access Dashboard
                  </Button>
                </Link> */}
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Final Stats Section */}
      {/* <section className="py-16 border-t border-border/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            variants={staggerContainer}
            className="text-center">
            <motion.div
              variants={fadeInUp}
              className="text-sm text-muted-foreground mb-4 tracking-wider uppercase">
              The Stronghold of Decentralized Trading
            </motion.div>

            <motion.div
              variants={fadeInUp}
              className="text-lg font-semibold text-foreground mb-2">
              POPULATION 10,000+ • EST 2024
            </motion.div>

            <motion.div
              variants={fadeInUp}
              className="text-2xl font-bold bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent">
              TRADE STRONG
            </motion.div>
          </motion.div>
        </div>
      </section> */}
    </div>
  );
}
