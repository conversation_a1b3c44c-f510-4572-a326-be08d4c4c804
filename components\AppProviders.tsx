"use client";
import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { ThemeProvider } from "@/components/theme-provider";
import SupabaseProvider from "@/components/SupabaseProvider";

import { Suspense } from "react";
import { SidebarProvider } from "./ui/sidebar";

export default function AppProviders({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <ClerkProvider
        publishableKey={process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}
      >
        <SupabaseProvider>
          <SidebarProvider>
            <Suspense fallback={<div className="flex items-center justify-center min-h-screen"><div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" /></div>}>{children}</Suspense>
          </SidebarProvider>
        </SupabaseProvider>
      </ClerkProvider>
    </ThemeProvider>
  );
}
