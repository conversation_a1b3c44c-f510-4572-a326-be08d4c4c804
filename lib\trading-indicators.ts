/**
 * Real trading indicators implementation
 * Supports RSI, MACD, Moving Averages, and other technical indicators
 */

export interface PricePoint {
  timestamp: number;
  price: number;
  volume?: number;
}

export interface RSIResult {
  value: number;
  signal: "BUY" | "SELL" | "HOLD";
  strength: "STRONG" | "MODERATE" | "WEAK";
}

export interface MACDResult {
  macd: number;
  signal: number;
  histogram: number;
  crossover: "BULLISH" | "BEARISH" | "NONE";
}

export interface MovingAverageResult {
  sma: number;
  ema: number;
  trend: "UPTREND" | "DOWNTREND" | "SIDEWAYS";
}

export interface EMACrossResult {
  fastEMA: number;
  slowEMA: number;
  signal: "BUY" | "SELL" | "HOLD";
  crossover: "BULLISH" | "BEARISH" | "NONE";
  strength: "STRONG" | "MODERATE" | "WEAK";
}

export interface TradingSignal {
  action: "BUY" | "SELL" | "HOLD";
  confidence: number; // 0-100
  indicators: {
    rsi?: RSIResult;
    macd?: MACDResult;
    ma?: MovingAverageResult;
    emaCross?: EMACrossResult;
  };
  timestamp: number;
  price: number;
}

// RSI (Relative Strength Index) Calculator
export class RSICalculator {
  private period: number;
  private gains: number[] = [];
  private losses: number[] = [];

  constructor(period: number = 14) {
    this.period = period;
  }

  calculate(prices: PricePoint[]): RSIResult | null {
    if (prices.length < this.period + 1) {
      return null;
    }

    // Calculate price changes
    const changes: number[] = [];
    for (let i = 1; i < prices.length; i++) {
      changes.push(prices[i].price - prices[i - 1].price);
    }

    // Separate gains and losses
    const gains = changes.map((change) => (change > 0 ? change : 0));
    const losses = changes.map((change) => (change < 0 ? Math.abs(change) : 0));

    // Calculate average gains and losses
    const avgGain =
      gains.slice(-this.period).reduce((sum, gain) => sum + gain, 0) /
      this.period;
    const avgLoss =
      losses.slice(-this.period).reduce((sum, loss) => sum + loss, 0) /
      this.period;

    if (avgLoss === 0) {
      return { value: 100, signal: "SELL", strength: "STRONG" };
    }

    const rs = avgGain / avgLoss;
    const rsi = 100 - 100 / (1 + rs);

    // Determine signal
    let signal: "BUY" | "SELL" | "HOLD" = "HOLD";
    let strength: "STRONG" | "MODERATE" | "WEAK" = "WEAK";

    if (rsi <= 30) {
      signal = "BUY";
      strength = rsi <= 20 ? "STRONG" : "MODERATE";
    } else if (rsi >= 70) {
      signal = "SELL";
      strength = rsi >= 80 ? "STRONG" : "MODERATE";
    }

    return { value: rsi, signal, strength };
  }
}

// MACD (Moving Average Convergence Divergence) Calculator
export class MACDCalculator {
  private fastPeriod: number;
  private slowPeriod: number;
  private signalPeriod: number;

  constructor(
    fastPeriod: number = 12,
    slowPeriod: number = 26,
    signalPeriod: number = 9,
  ) {
    this.fastPeriod = fastPeriod;
    this.slowPeriod = slowPeriod;
    this.signalPeriod = signalPeriod;
  }

  calculate(prices: PricePoint[]): MACDResult | null {
    if (prices.length < this.slowPeriod + this.signalPeriod) {
      return null;
    }

    const priceValues = prices.map((p) => p.price);

    // Calculate EMAs
    const fastEMA = this.calculateEMA(priceValues, this.fastPeriod);
    const slowEMA = this.calculateEMA(priceValues, this.slowPeriod);

    if (!fastEMA || !slowEMA) return null;

    // Calculate MACD line
    const macd = fastEMA - slowEMA;

    // Calculate signal line (EMA of MACD)
    // const macdHistory = [macd]; // In real implementation, you'd maintain history - unused for now
    const signal = macd; // Simplified - should be EMA of MACD history

    // Calculate histogram
    const histogram = macd - signal;

    // Determine crossover
    let crossover: "BULLISH" | "BEARISH" | "NONE" = "NONE";
    if (macd > signal && histogram > 0) {
      crossover = "BULLISH";
    } else if (macd < signal && histogram < 0) {
      crossover = "BEARISH";
    }

    return { macd, signal, histogram, crossover };
  }

  private calculateEMA(prices: number[], period: number): number | null {
    if (prices.length < period) return null;

    const multiplier = 2 / (period + 1);
    let ema =
      prices.slice(0, period).reduce((sum, price) => sum + price, 0) / period;

    for (let i = period; i < prices.length; i++) {
      ema = prices[i] * multiplier + ema * (1 - multiplier);
    }

    return ema;
  }
}

// Moving Average Calculator
export class MovingAverageCalculator {
  private period: number;

  constructor(period: number = 20) {
    this.period = period;
  }

  calculate(prices: PricePoint[]): MovingAverageResult | null {
    if (prices.length < this.period) {
      return null;
    }

    const priceValues = prices.map((p) => p.price);

    // Simple Moving Average
    const sma =
      priceValues.slice(-this.period).reduce((sum, price) => sum + price, 0) /
      this.period;

    // Exponential Moving Average
    const ema = this.calculateEMA(priceValues, this.period);

    if (!ema) return null;

    // Determine trend
    const currentPrice = priceValues[priceValues.length - 1];
    let trend: "UPTREND" | "DOWNTREND" | "SIDEWAYS" = "SIDEWAYS";

    const priceAboveSMA = currentPrice > sma;
    const priceAboveEMA = currentPrice > ema;
    const smaAboveEMA = sma > ema;

    if (priceAboveSMA && priceAboveEMA && smaAboveEMA) {
      trend = "UPTREND";
    } else if (!priceAboveSMA && !priceAboveEMA && !smaAboveEMA) {
      trend = "DOWNTREND";
    }

    return { sma, ema, trend };
  }

  private calculateEMA(prices: number[], period: number): number | null {
    if (prices.length < period) return null;

    const multiplier = 2 / (period + 1);
    let ema =
      prices.slice(0, period).reduce((sum, price) => sum + price, 0) / period;

    for (let i = period; i < prices.length; i++) {
      ema = prices[i] * multiplier + ema * (1 - multiplier);
    }

    return ema;
  }
}

// EMA Cross Calculator
export class EMACrossCalculator {
  private fastPeriod: number;
  private slowPeriod: number;
  private previousFastEMA?: number;
  private previousSlowEMA?: number;

  constructor(fastPeriod: number = 12, slowPeriod: number = 26) {
    this.fastPeriod = fastPeriod;
    this.slowPeriod = slowPeriod;
  }

  calculate(prices: PricePoint[]): EMACrossResult | null {
    if (prices.length < Math.max(this.fastPeriod, this.slowPeriod)) {
      return null;
    }

    const priceValues = prices.map((p) => p.price);

    // Calculate fast and slow EMAs
    const fastEMA = this.calculateEMA(priceValues, this.fastPeriod);
    const slowEMA = this.calculateEMA(priceValues, this.slowPeriod);

    if (!fastEMA || !slowEMA) return null;

    // Determine crossover type
    let crossover: "BULLISH" | "BEARISH" | "NONE" = "NONE";
    let signal: "BUY" | "SELL" | "HOLD" = "HOLD";
    let strength: "STRONG" | "MODERATE" | "WEAK" = "WEAK";

    // Check for crossover if we have previous values
    if (this.previousFastEMA && this.previousSlowEMA) {
      const previousCross = this.previousFastEMA > this.previousSlowEMA;
      const currentCross = fastEMA > slowEMA;

      if (!previousCross && currentCross) {
        // Fast EMA crossed above slow EMA - Bullish signal
        crossover = "BULLISH";
        signal = "BUY";

        // Calculate strength based on EMA separation
        const separation = Math.abs(fastEMA - slowEMA) / slowEMA;
        if (separation > 0.02) strength = "STRONG";
        else if (separation > 0.01) strength = "MODERATE";
        else strength = "WEAK";
      } else if (previousCross && !currentCross) {
        // Fast EMA crossed below slow EMA - Bearish signal
        crossover = "BEARISH";
        signal = "SELL";

        // Calculate strength based on EMA separation
        const separation = Math.abs(fastEMA - slowEMA) / slowEMA;
        if (separation > 0.02) strength = "STRONG";
        else if (separation > 0.01) strength = "MODERATE";
        else strength = "WEAK";
      }
    }

    // Store current values for next calculation
    this.previousFastEMA = fastEMA;
    this.previousSlowEMA = slowEMA;

    return {
      fastEMA,
      slowEMA,
      signal,
      crossover,
      strength,
    };
  }

  private calculateEMA(prices: number[], period: number): number | null {
    if (prices.length < period) return null;

    const multiplier = 2 / (period + 1);
    let ema =
      prices.slice(0, period).reduce((sum, price) => sum + price, 0) / period;

    for (let i = period; i < prices.length; i++) {
      ema = prices[i] * multiplier + ema * (1 - multiplier);
    }

    return ema;
  }

  // Reset the calculator state (useful when switching coins or restarting)
  reset(): void {
    this.previousFastEMA = undefined;
    this.previousSlowEMA = undefined;
  }
}

// Main Trading Signal Generator
export class TradingSignalGenerator {
  private rsiCalculator: RSICalculator;
  private macdCalculator: MACDCalculator;
  private maCalculator: MovingAverageCalculator;
  private emaCrossCalculator: EMACrossCalculator;

  constructor(
    rsiPeriod: number = 14,
    macdFast: number = 12,
    macdSlow: number = 26,
    macdSignal: number = 9,
    maPeriod: number = 20,
    emaCrossFast: number = 12,
    emaCrossSlow: number = 26,
  ) {
    this.rsiCalculator = new RSICalculator(rsiPeriod);
    this.macdCalculator = new MACDCalculator(macdFast, macdSlow, macdSignal);
    this.maCalculator = new MovingAverageCalculator(maPeriod);
    this.emaCrossCalculator = new EMACrossCalculator(
      emaCrossFast,
      emaCrossSlow,
    );
  }

  generateSignal(
    prices: PricePoint[],
    strategy?: string,
  ): TradingSignal | null {
    if (prices.length < 30) {
      // Need sufficient data
      return null;
    }

    const currentPrice = prices[prices.length - 1].price;

    // If EMA Cross strategy, use only EMA Cross signals
    if (strategy === "EMA_CROSS") {
      const emaCross = this.emaCrossCalculator.calculate(prices);

      if (!emaCross) {
        return null;
      }

      // For EMA Cross, confidence is based on signal strength and crossover type
      let confidence = 50; // Base confidence

      if (emaCross.crossover !== "NONE") {
        confidence =
          emaCross.strength === "STRONG"
            ? 85
            : emaCross.strength === "MODERATE"
              ? 70
              : 55;
      }

      return {
        action: emaCross.signal,
        confidence,
        indicators: {
          emaCross,
        },
        timestamp: prices[prices.length - 1].timestamp,
        price: currentPrice,
      };
    }

    // Default multi-indicator approach for other strategies
    const rsi = this.rsiCalculator.calculate(prices);
    const macd = this.macdCalculator.calculate(prices);
    const ma = this.maCalculator.calculate(prices);

    if (!rsi || !macd || !ma) {
      return null;
    }

    // Calculate overall signal and confidence
    let buySignals = 0;
    let sellSignals = 0;
    let totalWeight = 0;

    // RSI signals (weight: 30%)
    const rsiWeight = 30;
    if (rsi.signal === "BUY") {
      buySignals += rsiWeight * (rsi.strength === "STRONG" ? 1 : 0.7);
    } else if (rsi.signal === "SELL") {
      sellSignals += rsiWeight * (rsi.strength === "STRONG" ? 1 : 0.7);
    }
    totalWeight += rsiWeight;

    // MACD signals (weight: 40%)
    const macdWeight = 40;
    if (macd.crossover === "BULLISH") {
      buySignals += macdWeight;
    } else if (macd.crossover === "BEARISH") {
      sellSignals += macdWeight;
    }
    totalWeight += macdWeight;

    // Moving Average signals (weight: 30%)
    const maWeight = 30;
    if (ma.trend === "UPTREND") {
      buySignals += maWeight * 0.8;
    } else if (ma.trend === "DOWNTREND") {
      sellSignals += maWeight * 0.8;
    }
    totalWeight += maWeight;

    // Determine final action
    let action: "BUY" | "SELL" | "HOLD" = "HOLD";
    let confidence = 0;

    if (buySignals > sellSignals) {
      action = "BUY";
      confidence = Math.min(100, (buySignals / totalWeight) * 100);
    } else if (sellSignals > buySignals) {
      action = "SELL";
      confidence = Math.min(100, (sellSignals / totalWeight) * 100);
    } else {
      confidence = 50; // Neutral
    }

    return {
      action,
      confidence: Math.round(confidence),
      indicators: { rsi, macd, ma },
      timestamp: Date.now(),
      price: currentPrice,
    };
  }
}

// Utility functions
export function formatIndicatorValue(
  value: number,
  decimals: number = 2,
): string {
  return value.toFixed(decimals);
}

export function getSignalColor(signal: "BUY" | "SELL" | "HOLD"): string {
  switch (signal) {
    case "BUY":
      return "#10b981"; // green
    case "SELL":
      return "#ef4444"; // red
    case "HOLD":
      return "#6b7280"; // gray
  }
}

export function getConfidenceLevel(
  confidence: number,
): "HIGH" | "MEDIUM" | "LOW" {
  if (confidence >= 75) return "HIGH";
  if (confidence >= 50) return "MEDIUM";
  return "LOW";
}
