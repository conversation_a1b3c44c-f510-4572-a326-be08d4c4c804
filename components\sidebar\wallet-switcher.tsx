"use client";

import * as React from "react";
import { useState } from "react";
import {
  ChevronsUpDown,
  Plus,
  Wallet,
  <PERSON>py,
  Eye,
  EyeOff,
  Trash2,
} from "lucide-react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";

import Image from "next/image";

// Solana wallet generation functions
const generateSolanaWallet = () => {
  // Generate a random 32-byte private key
  const privateKey = Array.from(crypto.getRandomValues(new Uint8Array(32)))
    .map((b) => b.toString(16).padStart(2, "0"))
    .join("");

  // Generate a mock Solana address (in real app, use @solana/web3.js)
  const address = Array.from(crypto.getRandomValues(new Uint8Array(32)))
    .map((b) => b.toString(36))
    .join("")
    .substring(0, 44);

  return { privateKey, address };
};

// Wallet interfaces
interface LiveWallet {
  id: string;
  name: string;
  address: string;
  privateKey: string;
  balance: number;
  type: "live";
}

interface PaperWallet {
  id: string;
  name: string;
  balance: number;
  type: "paper";
  currency: string; // e.g., 'SOL', 'ETH'
}

export function WalletSwitcher({
  wallet,
}: {
  wallet: {
    name: string;
    logo: React.ElementType;
    plan: string;
  }[];
}) {
  const { isMobile } = useSidebar();
  const [activeTeam] = React.useState(wallet[0]);
  const [showWalletModal, setShowWalletModal] = useState(false);
  const [activeTab, setActiveTab] = useState("live");

  // Mock wallet data - in real app, this would come from context/state management
  const [liveWallets, setLiveWallets] = useState<LiveWallet[]>([]);

  const [paperWallets, setPaperWallets] = useState<PaperWallet[]>([]);
  const [paperBalance, setPaperBalance] = useState<string>("1000");
  const [showPrivateKey, setShowPrivateKey] = useState<string | null>(null);

  if (!activeTeam) {
    return null;
  }

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                <Image
                  src="/logo.png"
                  alt="DexTrip Logo"
                  width={30}
                  height={30}
                  className="hidden sm:block"
                />
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight ml-1">
                <span className="truncate font-bold">DEXTRIP</span>
                <span className="truncate text-xs">{activeTeam.plan}</span>
              </div>
              <ChevronsUpDown className="ml-auto" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-(--radix-dropdown-menu-trigger-width) min-w-72 rounded-lg"
            align="start"
            side={isMobile ? "bottom" : "right"}
            sideOffset={4}
          >
            {/* Live Wallets Section */}
            <DropdownMenuLabel className="text-muted-foreground text-xs">
              Live Wallets
            </DropdownMenuLabel>
            {liveWallets.map((wallet) => (
              <DropdownMenuItem key={wallet.id} className="gap-2 p-3">
                <div className="flex items-center gap-2 flex-1">
                  <Wallet className="w-4 h-4 text-green-500" />
                  <div className="flex-1">
                    <div className="font-medium text-sm">{wallet.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {wallet.address.slice(0, 8)}...{wallet.address.slice(-8)}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">
                    {wallet.balance.toFixed(2)} SOL
                  </div>
                  <div className="text-xs text-muted-foreground">Live</div>
                </div>
              </DropdownMenuItem>
            ))}

            <DropdownMenuSeparator />

            {/* Paper Wallets Section */}
            <DropdownMenuLabel className="text-muted-foreground text-xs">
              Paper Wallets
            </DropdownMenuLabel>
            {paperWallets.map((wallet) => (
              <DropdownMenuItem key={wallet.id} className="gap-2 p-3">
                <div className="flex items-center gap-2 flex-1">
                  <div className="w-2 h-2 rounded-full bg-blue-500" />
                  <div className="flex-1">
                    <div className="font-medium text-sm">{wallet.name}</div>
                    <div className="text-xs text-muted-foreground">
                      Paper Trading
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium">
                    {wallet.balance.toLocaleString()} {wallet.currency}
                  </div>
                  <div className="text-xs text-muted-foreground">Virtual</div>
                </div>
              </DropdownMenuItem>
            ))}

            <DropdownMenuSeparator />

            {/* Add New Wallet */}
            <DropdownMenuItem
              className="gap-2 p-3"
              onClick={() => setShowWalletModal(true)}
            >
              <div className="flex size-6 items-center justify-center rounded-md border bg-transparent">
                <Plus className="size-4" />
              </div>
              <div className="text-muted-foreground font-medium">
                Create New Wallet
              </div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>

      {/* Wallet Management Modal */}
      <Dialog open={showWalletModal} onOpenChange={setShowWalletModal}>
        <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Wallet className="h-5 w-5" />
              Wallet Management
            </DialogTitle>
            <DialogDescription>
              Add new wallets or manage your existing live and paper trading
              wallets.
            </DialogDescription>
          </DialogHeader>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="live">Live Wallets</TabsTrigger>
              <TabsTrigger value="paper">Paper Wallets</TabsTrigger>
            </TabsList>

            {/* Live Wallets Tab */}
            <TabsContent value="live" className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Create Solana Wallet</h3>
                  <Button
                    onClick={() => {
                      const { privateKey, address } = generateSolanaWallet();
                      const newWallet: LiveWallet = {
                        id: `live-${Date.now()}`,
                        name: `Wallet ${liveWallets.length + 1}`,
                        address,
                        privateKey,
                        balance: 0,
                        type: "live",
                      };
                      setLiveWallets([...liveWallets, newWallet]);
                      toast.success("New Solana wallet created!");
                    }}
                    className="bg-primary-gradient text-white"
                    size="sm"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create New Wallet
                  </Button>
                </div>

                {/* Live Wallets List */}
                <div className="space-y-3">
                  {liveWallets.map((wallet) => (
                    <Card key={wallet.id}>
                      <CardContent className="p-4">
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Wallet className="w-4 h-4 text-green-500" />
                              <span className="font-medium">{wallet.name}</span>
                            </div>
                            <div className="text-sm font-medium">
                              {wallet.balance.toFixed(2)} SOL
                            </div>
                          </div>

                          <div className="space-y-2">
                            <div>
                              <Label className="text-xs text-muted-foreground">
                                Address
                              </Label>
                              <div className="flex items-center gap-2">
                                <code className="text-xs bg-muted p-1 rounded flex-1">
                                  {wallet.address}
                                </code>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    navigator.clipboard.writeText(
                                      wallet.address,
                                    );
                                    toast.success("Address copied!");
                                  }}
                                >
                                  <Copy className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>

                            <div>
                              <Label className="text-xs text-muted-foreground">
                                Private Key
                              </Label>
                              <div className="flex items-center gap-2">
                                <code className="text-xs bg-muted p-1 rounded flex-1">
                                  {showPrivateKey === wallet.id
                                    ? wallet.privateKey
                                    : "•".repeat(64)}
                                </code>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setShowPrivateKey(
                                      showPrivateKey === wallet.id
                                        ? null
                                        : wallet.id,
                                    );
                                  }}
                                >
                                  {showPrivateKey === wallet.id ? (
                                    <EyeOff className="h-3 w-3" />
                                  ) : (
                                    <Eye className="h-3 w-3" />
                                  )}
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    navigator.clipboard.writeText(
                                      wallet.privateKey,
                                    );
                                    toast.success("Private key copied!");
                                  }}
                                >
                                  <Copy className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </div>

                          <div className="text-xs text-muted-foreground">
                            Transfer SOL to this address to start trading
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </TabsContent>

            {/* Paper Wallets Tab */}
            <TabsContent value="paper" className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Paper Trading</h3>
                </div>

                {/* Add New Paper Wallet Form */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">
                      Create Paper Wallet
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="paper-balance">
                        Starting Balance (SOL)
                      </Label>
                      <Input
                        id="paper-balance"
                        type="number"
                        placeholder="1000"
                        value={paperBalance}
                        onChange={(e) => setPaperBalance(e.target.value)}
                      />
                    </div>
                    <Button
                      onClick={() => {
                        const balance = parseFloat(paperBalance) || 1000;
                        const newWallet: PaperWallet = {
                          id: `paper-${Date.now()}`,
                          name: `P${paperWallets.length + 1}`,
                          balance,
                          type: "paper",
                          currency: "SOL",
                        };
                        setPaperWallets([...paperWallets, newWallet]);
                        setPaperBalance("1000");
                        toast.success(
                          `Paper wallet P${paperWallets.length + 1} created!`,
                        );
                      }}
                      className="w-full bg-primary-gradient text-white"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Create Paper Wallet
                    </Button>
                  </CardContent>
                </Card>

                {/* Paper Wallets List */}
                <div className="space-y-2">
                  {paperWallets.map((wallet) => (
                    <Card key={wallet.id}>
                      <CardContent className="p-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 rounded-full bg-blue-500" />
                            <span className="font-medium">{wallet.name}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium">
                              {wallet.balance.toLocaleString()} SOL
                            </span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                const updatedWallets = paperWallets.filter(
                                  (w) => w.id !== wallet.id,
                                );
                                setPaperWallets(updatedWallets);
                                toast.success("Paper wallet deleted!");
                              }}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>
    </SidebarMenu>
  );
}
