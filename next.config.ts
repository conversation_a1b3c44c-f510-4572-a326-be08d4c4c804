import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "dd.dexscreener.com", // DexScreener images
      },
      {
        protocol: "https",
        hostname: "assets.coingecko.com", // CoinGecko assets
      },
      {
        protocol: "https",
        hostname: "coin-images.coingecko.com", // CoinGecko coin images
      },
      {
        protocol: "https",
        hostname: "api.dicebear.com", // Fallback avatar generator
      },
    ],
  },
};

export default nextConfig;
