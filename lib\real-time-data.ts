// Real-time data service with CoinGecko integration for cryptocurrency data

export interface CoinData {
  id: string;
  symbol: string;
  name: string;
  current_price: number;
  price_change_24h: number;
  market_cap: number;
  volume_24h: number;
  last_updated: string;
}

export interface PriceUpdate {
  symbol: string;
  price: number;
  timestamp: number;
  change_24h: number;
  volume_24h: number;
}

export interface HistoricalPrice {
  timestamp: number;
  price: number;
  volume?: number;
}

export interface WebSocketMessage {
  txType: string;
  mint?: string;
  virtual_sol_reserves?: number;
  [key: string]: unknown;
}

export interface NewCoinListing {
  id: string;
  symbol: string;
  name: string;
  address: string;
  current_price: number;
  price_change_24h: number;
  price_change_1h: number;
  price_change_5m: number;
  volume_24h: number;
  volume_1h: number;
  market_cap: number;
  liquidity: number;
  fdv: number;
  image_url?: string;
  dex_id: string;
  pair_address: string;
  url: string;
  created_at: number;
  last_updated: string;
  chain: "solana" | "ethereum";
  age_hours: number;
  holders?: number;
  transactions_24h?: number;
  buys_24h?: number;
  sells_24h?: number;
  price_change_percentage_24h?: number;
  // Pump.fun specific fields
  sol_amount?: number; // Amount of SOL in the bonding curve
  pump_stage?: "launched" | "graduating" | "graduated" | "matured";
  graduation_time?: number; // Timestamp when token graduated (crossed 400 SOL)
  is_pump_token?: boolean; // Whether this is a pump.fun token
}

// CoinGecko API integration
class CoinGeckoProvider {
  protected baseUrl = "https://api.coingecko.com/api/v3";
  protected rateLimitDelay = 1000; // 1 second between requests for free tier

  async getCoinData(coinIds: string[]): Promise<CoinData[]> {
    try {
      const ids = coinIds.join(",");
      const response = await fetch(
        `${getApiBaseUrl()}/api/proxy/coingecko?endpoint=markets&ids=${ids}`,
      );

      if (!response.ok) {
        throw new Error(`CoinGecko API error: ${response.status}`);
      }

      const data = await response.json();
      return data.map(
        (coin: {
          id: string;
          symbol: string;
          name: string;
          current_price: number;
          price_change_percentage_24h?: number;
          market_cap?: number;
          total_volume?: number;
          image: string;
          last_updated: string;
        }) => ({
          id: coin.id,
          symbol: coin.symbol.toUpperCase(),
          name: coin.name,
          current_price: coin.current_price,
          price_change_24h: coin.price_change_percentage_24h || 0,
          market_cap: coin.market_cap || 0,
          volume_24h: coin.total_volume || 0,
          last_updated: coin.last_updated,
        }),
      );
    } catch (error) {
      console.error("CoinGecko API error:", error);
      return [];
    }
  }

  async getHistoricalPrices(
    coinId: string,
    days: number = 7,
  ): Promise<HistoricalPrice[]> {
    try {
      await new Promise((resolve) => setTimeout(resolve, this.rateLimitDelay));

      const response = await fetch(
        `${getApiBaseUrl()}/api/proxy/coingecko?endpoint=market_chart&coinId=${coinId}&days=${days}`,
      );

      if (!response.ok) {
        throw new Error(`CoinGecko API error: ${response.status}`);
      }

      const data = await response.json();
      return data.prices.map(([timestamp, price]: [number, number]) => ({
        timestamp,
        price,
      }));
    } catch (error) {
      console.error("CoinGecko historical data error:", error);
      return [];
    }
  }
}

// Pump.fun WebSocket data interface
export interface PumpFunTokenData {
  mint: string;
  name: string;
  symbol: string;
  description?: string;
  image_uri?: string;
  metadata_uri?: string;
  twitter?: string;
  telegram?: string;
  bonding_curve?: string;
  associated_bonding_curve?: string;
  creator?: string;
  created_timestamp: number;
  raydium_pool?: string;
  complete: boolean;
  virtual_sol_reserves: number;
  virtual_token_reserves: number;
  total_supply: number;
  website?: string;
  show_name: boolean;
  king_of_the_hill_timestamp?: number;
  market_cap: number;
  reply_count: number;
  last_reply?: number;
  nsfw: boolean;
  market_id?: string;
  inverted?: boolean;
  is_currently_live?: boolean;
  username?: string;
  profile_image?: string;
  usd_market_cap: number;
}

// Pump.fun WebSocket service for real-time trench data
class PumpFunWebSocketService {
  private ws: WebSocket | null = null;
  private listeners: Set<(tokens: NewCoinListing[]) => void> = new Set();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 5000;
  private isConnected = false;

  // Store tokens by stage
  private launchedTokens: Map<string, NewCoinListing> = new Map();
  private graduatingTokens: Map<string, NewCoinListing> = new Map();
  private graduatedTokens: Map<string, NewCoinListing> = new Map();
  private maturedTokens: Map<string, NewCoinListing> = new Map();

  constructor() {
    this.connect();
  }

  private connect() {
    if (typeof window === "undefined") return;

    try {
      const apiKey = process.env.NEXT_PUBLIC_PUMP_API_KEY;
      const wsUrl = `wss://pumpportal.fun/api/data${
        apiKey ? `?api-key=${apiKey}` : ""
      }`;

      console.log("🔌 Connecting to Pump.fun WebSocket...");
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log("✅ Pump.fun WebSocket connected");
        this.isConnected = true;
        this.reconnectAttempts = 0;

        // Subscribe to new token events
        this.subscribeToEvents();
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.handleWebSocketMessage(data);
        } catch (error) {
          console.error("❌ Error parsing Pump.fun WebSocket message:", error);
        }
      };

      this.ws.onclose = () => {
        console.log("🔌 Pump.fun WebSocket disconnected");
        this.isConnected = false;
        this.attemptReconnect();
      };

      this.ws.onerror = (error) => {
        console.error("❌ Pump.fun WebSocket error:", error);
      };
    } catch (error) {
      console.error("❌ Pump.fun WebSocket connection error:", error);
    }
  }

  private subscribeToEvents() {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) return;

    // Subscribe to new token creation events
    this.ws.send(
      JSON.stringify({
        method: "subscribeNewToken",
      }),
    );

    // Subscribe to token trade events to track SOL amounts
    this.ws.send(
      JSON.stringify({
        method: "subscribeTokenTrade",
      }),
    );

    console.log("📡 Subscribed to Pump.fun events");
  }

  private handleWebSocketMessage(data: WebSocketMessage) {
    try {
      if (data.txType === "create") {
        this.handleNewToken(data as unknown as PumpFunTokenData);
      } else if (data.txType === "buy" || data.txType === "sell") {
        this.handleTokenTrade(data);
      }
    } catch (error) {
      console.error("❌ Error handling WebSocket message:", error);
    }
  }

  private handleNewToken(data: PumpFunTokenData) {
    const token: NewCoinListing = {
      id: data.mint,
      symbol: data.symbol,
      name: data.name,
      address: data.mint,
      current_price: 0,
      price_change_24h: 0,
      price_change_1h: 0,
      price_change_5m: 0,
      volume_24h: 0,
      volume_1h: 0,
      market_cap: data.usd_market_cap || 0,
      liquidity: 0,
      fdv: 0,
      image_url: data.image_uri,
      dex_id: "pumpfun",
      pair_address: data.bonding_curve || "",
      url: `https://pump.fun/${data.mint}`,
      created_at: data.created_timestamp,
      last_updated: new Date().toISOString(),
      chain: "solana",
      age_hours: (Date.now() - data.created_timestamp) / (1000 * 60 * 60),
      sol_amount: data.virtual_sol_reserves || 0,
      pump_stage: "launched",
      is_pump_token: true,
    };

    this.launchedTokens.set(data.mint, token);
    this.notifyListeners();

    console.log(`🚀 New token launched: ${data.symbol} (${data.mint})`);
  }

  private handleTokenTrade(data: WebSocketMessage) {
    const mint = data.mint;
    const solAmount = data.virtual_sol_reserves || 0;

    if (!mint) return;

    // Find token in any stage
    const token =
      this.launchedTokens.get(mint) ||
      this.graduatingTokens.get(mint) ||
      this.graduatedTokens.get(mint) ||
      this.maturedTokens.get(mint);

    if (!token) return;

    // Update SOL amount
    token.sol_amount = solAmount;
    token.last_updated = new Date().toISOString();

    // Determine new stage based on SOL amount
    const newStage = this.determinePumpStage(solAmount, token.graduation_time);
    const currentStage = token.pump_stage;

    if (newStage !== currentStage) {
      // Remove from current stage
      this.removeTokenFromStage(mint, currentStage);

      // Add to new stage
      token.pump_stage = newStage;
      if (newStage === "graduated" && !token.graduation_time) {
        token.graduation_time = Date.now();
      }
      this.addTokenToStage(mint, token, newStage);

      console.log(
        `📈 Token ${token.symbol} moved from ${currentStage} to ${newStage} (${solAmount} SOL)`,
      );
      this.notifyListeners();
    } else {
      // Update existing token in same stage
      this.addTokenToStage(mint, token, newStage);
    }
  }

  private determinePumpStage(
    solAmount: number,
    graduationTime?: number,
  ): "launched" | "graduating" | "graduated" | "matured" {
    if (solAmount >= 400) {
      // Check if token has been above 400 SOL for more than 24 hours
      if (graduationTime && Date.now() - graduationTime > 24 * 60 * 60 * 1000) {
        return "matured";
      }
      return "graduated";
    } else if (solAmount >= 300) {
      // Nearing 400 SOL
      return "graduating";
    }
    return "launched";
  }

  private removeTokenFromStage(mint: string, stage?: string) {
    if (!stage) return;

    switch (stage) {
      case "launched":
        this.launchedTokens.delete(mint);
        break;
      case "graduating":
        this.graduatingTokens.delete(mint);
        break;
      case "graduated":
        this.graduatedTokens.delete(mint);
        break;
      case "matured":
        this.maturedTokens.delete(mint);
        break;
    }
  }

  private addTokenToStage(mint: string, token: NewCoinListing, stage: string) {
    switch (stage) {
      case "launched":
        this.launchedTokens.set(mint, token);
        break;
      case "graduating":
        this.graduatingTokens.set(mint, token);
        break;
      case "graduated":
        this.graduatedTokens.set(mint, token);
        break;
      case "matured":
        this.maturedTokens.set(mint, token);
        break;
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(
        `🔄 Attempting to reconnect to Pump.fun WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`,
      );
      setTimeout(() => this.connect(), this.reconnectDelay);
    } else {
      console.error(
        "❌ Max reconnection attempts reached for Pump.fun WebSocket",
      );
    }
  }

  private notifyListeners() {
    const allTokens = [
      ...Array.from(this.launchedTokens.values()),
      ...Array.from(this.graduatingTokens.values()),
      ...Array.from(this.graduatedTokens.values()),
      ...Array.from(this.maturedTokens.values()),
    ];

    this.listeners.forEach((callback) => callback(allTokens));
  }

  // Public methods
  subscribe(callback: (tokens: NewCoinListing[]) => void): () => void {
    this.listeners.add(callback);

    // Immediately call with current data
    this.notifyListeners();

    return () => {
      this.listeners.delete(callback);
    };
  }

  getTokensByStage(
    stage: "launched" | "graduating" | "graduated" | "matured",
  ): NewCoinListing[] {
    switch (stage) {
      case "launched":
        return Array.from(this.launchedTokens.values());
      case "graduating":
        return Array.from(this.graduatingTokens.values());
      case "graduated":
        return Array.from(this.graduatedTokens.values());
      case "matured":
        return Array.from(this.maturedTokens.values());
      default:
        return [];
    }
  }

  getAllTokens(): NewCoinListing[] {
    return [
      ...Array.from(this.launchedTokens.values()),
      ...Array.from(this.graduatingTokens.values()),
      ...Array.from(this.graduatedTokens.values()),
      ...Array.from(this.maturedTokens.values()),
    ];
  }

  get connected(): boolean {
    return this.isConnected;
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.listeners.clear();
    this.launchedTokens.clear();
    this.graduatingTokens.clear();
    this.graduatedTokens.clear();
    this.maturedTokens.clear();
  }
}

// WebSocket price feed for real-time updates
class RealTimePriceFeed {
  private ws: WebSocket | null = null;
  private subscribers = new Map<string, Set<(update: PriceUpdate) => void>>();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 5000;

  constructor() {
    this.connect();
  }

  private connect() {
    if (typeof window === "undefined") return;

    try {
      // Using a mock WebSocket for demo - replace with actual price feed
      this.ws = new WebSocket("wss://echo.websocket.org");

      this.ws.onopen = () => {
        console.log("Price feed connected");
        this.reconnectAttempts = 0;
      };

      this.ws.onmessage = (event) => {
        try {
          // Skip non-JSON messages from echo WebSocket
          if (typeof event.data === "string" && event.data.startsWith("{")) {
            const update: PriceUpdate = JSON.parse(event.data);
            this.notifySubscribers(update);
          }
        } catch {
          // Silently ignore JSON parsing errors from echo WebSocket
          console.debug("Price feed message (non-JSON):", event.data);
        }
      };

      this.ws.onclose = () => {
        console.log("Price feed disconnected");
        this.attemptReconnect();
      };

      this.ws.onerror = (error) => {
        console.error("Price feed error:", error);
      };
    } catch (error) {
      console.error("Price feed connection error:", error);
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => this.connect(), this.reconnectDelay);
    }
  }

  private notifySubscribers(update: PriceUpdate) {
    const symbolSubscribers = this.subscribers.get(update.symbol);
    if (symbolSubscribers) {
      symbolSubscribers.forEach((callback) => callback(update));
    }
  }

  subscribe(symbol: string, callback: (update: PriceUpdate) => void) {
    if (!this.subscribers.has(symbol)) {
      this.subscribers.set(symbol, new Set());
    }
    this.subscribers.get(symbol)!.add(callback);

    return () => {
      const symbolSubscribers = this.subscribers.get(symbol);
      if (symbolSubscribers) {
        symbolSubscribers.delete(callback);
        if (symbolSubscribers.size === 0) {
          this.subscribers.delete(symbol);
        }
      }
    };
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.subscribers.clear();
  }
}

// CoinGecko provider for cryptocurrency data
class CoinGeckoTokenProvider {
  private coinGeckoService = new EnhancedCoinGeckoProvider();
  private listeners: Set<(tokens: NewCoinListing[]) => void> = new Set();
  private latestTokens: NewCoinListing[] = [];

  // Rate limiting and caching
  private lastApiCall = 0;
  private minApiInterval = 5000; // 5 seconds between API calls (CoinGecko free tier)
  private cache = new Map<string, { data: unknown; timestamp: number }>();
  private cacheTimeout = 300000; // 5 minutes cache timeout

  constructor() {
    // Initial fetch with delay
    setTimeout(() => this.fetchTrendingTokens(), 1000);
    // Set up periodic refresh every 5 minutes
    setInterval(() => this.fetchTrendingTokens(), 300000);
  }

  private async fetchTrendingTokens() {
    try {
      // Check rate limiting
      const now = Date.now();
      if (now - this.lastApiCall < this.minApiInterval) {
        console.log("⏳ Rate limiting: Using cached data");
        return;
      }

      // Check cache first
      const cacheKey = "trending_tokens";
      const cached = this.cache.get(cacheKey);
      if (cached && now - cached.timestamp < this.cacheTimeout) {
        console.log("📦 Using cached trending token data");
        this.latestTokens = cached.data as NewCoinListing[];
        this.notifyListeners();
        return;
      }

      console.log("📊 Fetching trending tokens from CoinGecko...");
      this.lastApiCall = now;

      // Get trending tokens from CoinGecko
      const trendingTokens = await this.coinGeckoService.getNewListings(50);

      this.latestTokens = trendingTokens;

      // Cache the results
      this.cache.set(cacheKey, { data: trendingTokens, timestamp: now });

      console.log(`🎯 Loaded ${trendingTokens.length} trending tokens`);
      this.notifyListeners();
    } catch (error) {
      console.error("❌ Error fetching trending tokens:", error);
      // Use mock data as fallback
      if (this.latestTokens.length === 0) {
        this.latestTokens = this.createMockTokens();
        console.log("🔄 Using mock data as fallback");
        this.notifyListeners();
      }
    }
  }

  private notifyListeners() {
    this.listeners.forEach((listener) => {
      try {
        listener(this.latestTokens);
      } catch (error) {
        console.error("Error notifying listener:", error);
      }
    });
  }

  private createMockTokens(): NewCoinListing[] {
    // Use fixed timestamp to avoid hydration mismatches
    const now = 1703097600000; // Fixed timestamp: 2023-12-20 12:00:00 UTC
    return [
      {
        id: "mock-token-1",
        name: "Mock Trending Token",
        symbol: "TREND",
        address: "TrendTokenAddress123",
        current_price: 0.000001,
        price_change_24h: 15.5,
        price_change_1h: 5.2,
        price_change_5m: 2.1,
        volume_24h: 10000,
        volume_1h: 1500,
        market_cap: 50000,
        liquidity: 25000,
        fdv: 60000,
        image_url:
          "https://assets.coingecko.com/coins/images/1/small/bitcoin.png",
        dex_id: "uniswap",
        pair_address: "TrendPairAddress123",
        url: "https://dexscreener.com/ethereum/TrendPairAddress123",
        created_at: now - 120000, // 2 minutes ago
        last_updated: new Date().toISOString(),
        chain: "ethereum",
        age_hours: 0.033, // 2 minutes
        holders: 45,
        transactions_24h: 120,
        buys_24h: 80,
        sells_24h: 40,
        price_change_percentage_24h: 15.5,
      },
      {
        id: "mock-token-2",
        name: "Demo Trending Coin",
        symbol: "DEMO",
        address: "DemoTrendAddress456",
        current_price: 0.000002,
        price_change_24h: -5.2,
        price_change_1h: -2.1,
        price_change_5m: -0.8,
        volume_24h: 15000,
        volume_1h: 2200,
        market_cap: 75000,
        liquidity: 35000,
        fdv: 90000,
        image_url:
          "https://assets.coingecko.com/coins/images/279/small/ethereum.png",
        dex_id: "pancakeswap",
        pair_address: "DemoTrendPair456",
        url: "https://dexscreener.com/ethereum/DemoTrendPair456",
        created_at: now - 300000, // 5 minutes ago
        last_updated: new Date().toISOString(),
        chain: "ethereum",
        age_hours: 0.083, // 5 minutes
        holders: 78,
        transactions_24h: 200,
        buys_24h: 110,
        sells_24h: 90,
        price_change_percentage_24h: -5.2,
      },
    ];
  }

  // Subscribe to real-time token updates
  subscribe(callback: (tokens: NewCoinListing[]) => void): () => void {
    this.listeners.add(callback);

    // Immediately call with current data if available
    if (this.latestTokens.length > 0) {
      callback(this.latestTokens);
    }

    return () => {
      this.listeners.delete(callback);
    };
  }

  // Get current tokens
  getLatestTokens(): NewCoinListing[] {
    return this.latestTokens;
  }

  // Force refresh tokens
  async refreshTokens(): Promise<void> {
    await this.fetchTrendingTokens();
  }

  // Check if connected (always true for CoinGecko provider)
  get isConnected(): boolean {
    return true;
  }

  disconnect() {
    this.listeners.clear();
  }
}

// DexScreener API provider
class DexScreenerProvider {
  private baseUrl = "https://api.dexscreener.com/latest";
  private rateLimitDelay = 500; // 500ms between requests

  async getTokenInfo(address: string): Promise<{
    name: string;
    symbol: string;
    image_url: string;
    price: number;
    volume_24h: number;
    market_cap: number;
    price_change_24h: number;
    liquidity: number;
  } | null> {
    try {
      await new Promise((resolve) => setTimeout(resolve, this.rateLimitDelay));

      const response = await fetch(`${this.baseUrl}/dex/tokens/${address}`);

      if (!response.ok) {
        console.warn(
          `DexScreener API error for ${address}: ${response.status}`,
        );
        return null;
      }

      const data = await response.json();

      if (!data.pairs || data.pairs.length === 0) {
        // Only log warning for tokens that look like addresses, not symbols
        if (address.length > 20) {
          console.warn(`No trading pairs found for token ${address}`);
        }
        return null;
      }

      // Get the most liquid pair
      const bestPair = data.pairs.reduce(
        (
          best: { liquidity?: { usd?: number } },
          current: { liquidity?: { usd?: number } },
        ) => {
          const currentLiquidity = current.liquidity?.usd || 0;
          const bestLiquidity = best.liquidity?.usd || 0;
          return currentLiquidity > bestLiquidity ? current : best;
        },
      );

      return {
        name: bestPair.baseToken?.name || `Token ${address.slice(-8)}`,
        symbol: bestPair.baseToken?.symbol || `TOKEN_${address.slice(-8)}`,
        image_url: bestPair.info?.imageUrl || "",
        price: parseFloat(bestPair.priceUsd || "0"),
        volume_24h: bestPair.volume?.h24 || 0,
        market_cap: bestPair.marketCap || 0,
        price_change_24h: bestPair.priceChange?.h24 || 0,
        liquidity: bestPair.liquidity?.usd || 0,
      };
    } catch (error) {
      console.error(`DexScreener API error for ${address}:`, error);
      return null;
    }
  }

  async enrichTokenData(tokens: NewCoinListing[]): Promise<NewCoinListing[]> {
    console.log(
      `🔍 Enriching ${tokens.length} tokens with DexScreener data...`,
    );

    const enrichedTokens: NewCoinListing[] = [];

    for (const token of tokens) {
      try {
        const dexData = await this.getTokenInfo(token.address);

        if (dexData) {
          enrichedTokens.push({
            ...token,
            name: dexData.name,
            symbol: dexData.symbol,
            image_url: dexData.image_url,
            current_price: dexData.price || token.current_price,
            volume_24h: dexData.volume_24h || token.volume_24h,
            market_cap: dexData.market_cap || token.market_cap,
            price_change_24h:
              dexData.price_change_24h || token.price_change_24h,
            liquidity: dexData.liquidity || token.liquidity,
            url: `https://dexscreener.com/solana/${token.address}`,
          });
          console.log(`✅ Enriched ${dexData.symbol} (${dexData.name})`);
        } else {
          // Keep original token if DexScreener data not available
          enrichedTokens.push({
            ...token,
            url: `https://dexscreener.com/solana/${token.address}`,
          });
          // Only log for tokens that should have DEX data (longer addresses)
          if (token.address && token.address.length > 20) {
            console.log(`⚠️ No DexScreener data for ${token.symbol}`);
          }
        }
      } catch (error) {
        console.error(`Error enriching token ${token.address}:`, error);
        enrichedTokens.push(token);
      }
    }

    console.log(`🎯 Successfully enriched ${enrichedTokens.length} tokens`);
    return enrichedTokens;
  }
}

// Helper function to get the correct API base URL
function getApiBaseUrl(): string {
  // Check if we're in a browser environment
  if (typeof window !== "undefined") {
    return window.location.origin;
  }

  // For server-side, use localhost in development or the deployment URL
  if (process.env.NODE_ENV === "development") {
    return "http://localhost:3000";
  }

  // For production, try to get the deployment URL
  return process.env.NEXT_PUBLIC_VERCEL_URL
    ? `https://${process.env.NEXT_PUBLIC_VERCEL_URL}`
    : "http://localhost:3000";
}

// Enhanced CoinGecko provider with new listings
class EnhancedCoinGeckoProvider extends CoinGeckoProvider {
  async getNewListings(limit: number = 50): Promise<NewCoinListing[]> {
    try {
      // Get recently added coins from CoinGecko via proxy
      const response = await fetch(
        `${getApiBaseUrl()}/api/proxy/coingecko?endpoint=new-listings&limit=${limit}`,
      );

      if (!response.ok) {
        throw new Error(`CoinGecko API error: ${response.status}`);
      }

      const data = await response.json();

      // Convert to NewCoinListing format
      const listings: NewCoinListing[] = data.map(
        (coin: {
          id: string;
          symbol: string;
          name: string;
          current_price: number;
          price_change_percentage_24h?: number;
          market_cap?: number;
          total_volume?: number;
          image: string;
          last_updated: string;
        }) => ({
          id: coin.id,
          symbol: coin.symbol.toUpperCase(),
          name: coin.name,
          address: coin.id, // CoinGecko uses ID as address
          current_price: coin.current_price || 0,
          price_change_24h: coin.price_change_percentage_24h || 0,
          price_change_1h: 0,
          price_change_5m: 0,
          volume_24h: coin.total_volume || 0,
          volume_1h: 0,
          market_cap: coin.market_cap || 0,
          liquidity: 0,
          fdv: 0, // fully_diluted_valuation not available in this type
          image_url: coin.image,
          dex_id: "coingecko",
          pair_address: coin.id,
          url: `https://www.coingecko.com/en/coins/${coin.id}`,
          created_at: Date.now() - Math.random() * 24 * 60 * 60 * 1000, // Random time in last 24h
          last_updated: coin.last_updated,
          chain: "ethereum", // Default to ethereum for CoinGecko
          age_hours: Math.random() * 24,
          transactions_24h: 0,
          buys_24h: 0,
          sells_24h: 0,
        }),
      );

      return listings;
    } catch (error) {
      console.error("Enhanced CoinGecko provider error:", error);
      return [];
    }
  }
}

// Main data service
export class RealTimeDataService {
  private coinGecko = new EnhancedCoinGeckoProvider();
  private coinGeckoTokens = new CoinGeckoTokenProvider();
  private dexScreener = new DexScreenerProvider();
  private priceFeed = new RealTimePriceFeed();
  private pumpFunWebSocket = new PumpFunWebSocketService();
  private cache = new Map<string, { data: unknown; timestamp: number }>();
  private cacheTimeout = 15000; // 15 second cache for real-time updates

  async getCoinData(symbols: string[]): Promise<CoinData[]> {
    const cacheKey = symbols.sort().join(",");
    const cached = this.cache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data as CoinData[];
    }

    try {
      const data = await this.coinGecko.getCoinData(symbols);
      this.cache.set(cacheKey, { data, timestamp: Date.now() });
      return data;
    } catch (error) {
      console.error("Failed to fetch coin data:", error);
      return (cached?.data as CoinData[]) || [];
    }
  }

  async getNewListings(
    chain: "solana" | "ethereum" = "solana",
    source: "all" | "pumpfun" | "raydium" | "dexscreener" = "all",
    limit: number = 50,
  ): Promise<NewCoinListing[]> {
    const cacheKey = `new-listings-${chain}-${source}-${limit}`;
    const cached = this.cache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data as NewCoinListing[];
    }

    try {
      console.log(`Fetching new listings for ${chain} from ${source}...`);
      let listings: NewCoinListing[] = [];

      if (chain === "solana") {
        // Prioritize pump.fun WebSocket data for real-time updates
        if (source === "all" || source === "pumpfun") {
          const pumpTokens = this.pumpFunWebSocket.getAllTokens();
          if (pumpTokens.length > 0) {
            listings = [...listings, ...pumpTokens];
            console.log(
              `🚀 Added ${pumpTokens.length} pump.fun tokens from WebSocket`,
            );
          }
        }

        // Fallback to CoinGecko provider for additional trending tokens
        if (listings.length < limit) {
          const trendingListings = this.coinGeckoTokens.getLatestTokens();
          const remainingSlots = limit - listings.length;
          listings = [
            ...listings,
            ...trendingListings.slice(0, remainingSlots),
          ];
          console.log(
            `🎯 Added ${Math.min(
              trendingListings.length,
              remainingSlots,
            )} trending tokens`,
          );
        }

        // Enrich with DexScreener data for better images and names
        if (listings.length > 0) {
          console.log(`🔍 Enriching tokens with DexScreener data...`);
          listings = await this.dexScreener.enrichTokenData(listings);
        }
      } else if (chain === "ethereum") {
        // Use CoinGecko for Ethereum tokens
        const coinGeckoListings = await this.coinGecko.getNewListings(limit);
        listings = [...listings, ...coinGeckoListings];
      }

      // Remove duplicates and sort by creation time
      const uniqueListings = this.removeDuplicates(listings);
      const sortedListings = uniqueListings.sort(
        (a, b) => b.created_at - a.created_at,
      );

      const result = sortedListings.slice(0, limit);
      this.cache.set(cacheKey, { data: result, timestamp: Date.now() });

      return result;
    } catch (error) {
      console.error("Failed to fetch new listings:", error);
      return (cached?.data as NewCoinListing[]) || [];
    }
  }

  private removeDuplicates(listings: NewCoinListing[]): NewCoinListing[] {
    const seen = new Set<string>();
    return listings.filter((listing) => {
      const key = listing.address || listing.id;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  subscribeToPrice(symbol: string, callback: (update: PriceUpdate) => void) {
    return this.priceFeed.subscribe(symbol, callback);
  }

  async getHistoricalPrices(
    coinId: string,
    days: number = 7,
  ): Promise<HistoricalPrice[]> {
    return this.coinGecko.getHistoricalPrices(coinId, days);
  }

  async getCoinFromDexScreener(
    address: string,
  ): Promise<NewCoinListing | null> {
    try {
      const dexData = await this.dexScreener.getTokenInfo(address);

      if (!dexData) {
        return null;
      }

      return {
        id: address,
        symbol: dexData.symbol,
        name: dexData.name,
        address: address,
        current_price: dexData.price,
        price_change_24h: dexData.price_change_24h,
        price_change_1h: 0,
        price_change_5m: 0,
        volume_24h: dexData.volume_24h,
        volume_1h: 0,
        market_cap: dexData.market_cap,
        liquidity: dexData.liquidity,
        fdv: dexData.market_cap,
        image_url: dexData.image_url,
        dex_id: "dexscreener",
        pair_address: address,
        url: `https://dexscreener.com/solana/${address}`,
        created_at: Date.now(),
        last_updated: new Date().toISOString(),
        chain: "solana",
        age_hours: 0,
      };
    } catch (error) {
      console.error(`Failed to get coin from DexScreener: ${address}`, error);
      return null;
    }
  }

  clearCache() {
    this.cache.clear();
    console.log("Cache cleared - next request will fetch fresh data");
  }

  // Force refresh by clearing cache and fetching new data
  async forceRefresh(
    chain: "solana" | "ethereum" = "solana",
    source: "all" | "pumpfun" | "raydium" | "dexscreener" = "all",
    limit: number = 50,
  ): Promise<NewCoinListing[]> {
    this.clearCache();
    return this.getNewListings(chain, source, limit);
  }

  async searchCoins(query: string): Promise<NewCoinListing[]> {
    try {
      // Search through current listings
      const listings = await this.getNewListings("solana", "all", 100);

      const searchTerm = query.toLowerCase();
      return listings.filter(
        (coin) =>
          coin.name.toLowerCase().includes(searchTerm) ||
          coin.symbol.toLowerCase().includes(searchTerm) ||
          coin.address?.toLowerCase().includes(searchTerm),
      );
    } catch (error) {
      console.error("Failed to search coins:", error);
      return [];
    }
  }

  async getTrendingCoins(
    chain: "solana" | "ethereum" = "solana",
  ): Promise<NewCoinListing[]> {
    try {
      // Get recent listings and sort by volume/activity
      const listings = await this.getNewListings(chain, "all", 50);

      // Sort by volume and price change to get trending coins
      return listings
        .filter((coin) => coin.volume_24h > 0)
        .sort((a, b) => {
          const aScore =
            (a.volume_24h || 0) + Math.abs(a.price_change_24h || 0) * 1000;
          const bScore =
            (b.volume_24h || 0) + Math.abs(b.price_change_24h || 0) * 1000;
          return bScore - aScore;
        })
        .slice(0, 20);
    } catch (error) {
      console.error("Failed to get trending coins:", error);
      return [];
    }
  }

  // Get pump.fun tokens by stage
  getPumpTokensByStage(
    stage: "launched" | "graduating" | "graduated" | "matured",
  ): NewCoinListing[] {
    return this.pumpFunWebSocket.getTokensByStage(stage);
  }

  // Subscribe to pump.fun real-time updates
  subscribeToPumpUpdates(
    callback: (tokens: NewCoinListing[]) => void,
  ): () => void {
    return this.pumpFunWebSocket.subscribe(callback);
  }

  // Check if pump.fun WebSocket is connected
  get isPumpConnected(): boolean {
    return this.pumpFunWebSocket.connected;
  }

  disconnect() {
    this.priceFeed.disconnect();
    this.coinGeckoTokens.disconnect();
    this.pumpFunWebSocket.disconnect();
    this.cache.clear();
  }
}

// Singleton instance
export const realTimeDataService = new RealTimeDataService();

// Utility functions
export function formatPrice(price: number): string {
  if (price < 0.01) {
    return price.toFixed(8);
  }
  return `$${price.toFixed(2)}`;
}

export function formatPriceChange(change: number): string {
  const sign = change >= 0 ? "+" : "";
  return `${sign}${change.toFixed(2)}%`;
}

export function formatVolume(volume: number): string {
  if (volume >= 1e9) {
    return `$${(volume / 1e9).toFixed(2)}B`;
  } else if (volume >= 1e6) {
    return `$${(volume / 1e6).toFixed(2)}M`;
  } else if (volume >= 1e3) {
    return `$${(volume / 1e3).toFixed(2)}K`;
  }
  return `$${volume.toFixed(2)}`;
}
