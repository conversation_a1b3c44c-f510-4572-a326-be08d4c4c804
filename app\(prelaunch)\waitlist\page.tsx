"use client";

import React from "react";
import { motion } from "framer-motion";
import {
  TrendingUp,
  Zap,
  Shield,
  BarChart3,
  Users,
  Star,
  CheckCircle,
} from "lucide-react";

export default function WaitlistPage() {
  const features = [
    {
      icon: <TrendingUp className="w-6 h-6" />,
      title: "Real-time Market Data",
      description:
        "Get instant access to Solana token prices and market movements",
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: "Lightning Fast Trading",
      description:
        "Execute trades in milliseconds with our optimized infrastructure",
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Secure & Reliable",
      description: "Bank-grade security with 99.9% uptime guarantee",
    },
    {
      icon: <BarChart3 className="w-6 h-6" />,
      title: "Advanced Analytics",
      description:
        "Professional trading tools and comprehensive market analysis",
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: "Community Driven",
      description: "Join thousands of traders in our vibrant community",
    },
    {
      icon: <Star className="w-6 h-6" />,
      title: "Premium Features",
      description: "Access exclusive features and priority customer support",
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Main Content */}
      <main className="px-6 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Hero Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center space-x-2 bg-green-500/20 dark:bg-green-500/20 text-green-600 dark:text-green-400 px-4 py-2 rounded-full mb-6 border border-green-200 dark:border-green-500/30">
              <CheckCircle className="w-5 h-5" />
              <span className="text-sm font-medium">
                Successfully Joined Waitlist
              </span>
            </div>

            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
              You&apos;re In!
            </h1>

            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
              Welcome to the future of Solana trading. You&apos;re now part of
              an exclusive group getting early access to DexTrip&apos;s
              revolutionary trading platform.
            </p>

            {/* Position Card */}
            {/* <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-block bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-sm border border-purple-500/30 rounded-2xl p-8 mb-12"
            >
              <div className="flex items-center justify-center space-x-3 mb-4">
                <Clock className="w-6 h-6 text-purple-400" />
                <span className="text-purple-400 font-medium">
                  Your Position
                </span>
              </div>
              <div className="text-4xl font-bold text-white mb-2">
                #{position}
              </div>
              <p className="text-gray-400">in the waitlist</p>
            </motion.div> */}
          </motion.div>

          {/* What's Coming Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mb-16"
          >
            {/* <h2 className="text-3xl font-bold text-white text-center mb-12">
              What's Coming Your Way
            </h2> */}

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 * index }}
                  className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-xl p-6 hover:bg-white dark:hover:bg-white/10 transition-colors"
                >
                  <div className="mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    {feature.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Next Steps */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-purple-200 dark:border-purple-500/20 rounded-2xl p-8 text-center "
          >
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              What Happens Next?
            </h3>
            <div className="grid md:grid-cols-3 gap-6 text-left">
              <div className="space-y-2">
                <div className="text-gray-900 dark:text-white font-semibold">
                  1. Stay Tuned
                </div>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  We&apos;ll send you updates on our progress and exclusive
                  previews
                </p>
              </div>
              <div className="space-y-2">
                <div className="text-gray-900 dark:text-white font-semibold">
                  2. Early Access
                </div>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  Get priority access when we launch the beta version
                </p>
              </div>
              <div className="space-y-2">
                <div className="text-gray-900 dark:text-white font-semibold">
                  3. Trade First
                </div>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  Be among the first to experience next-gen Solana trading
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </main>

      {/* Footer */}
      <footer className="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
        <p>&copy; 2024 DexTrip. All rights reserved.</p>
      </footer>
    </div>
  );
}
