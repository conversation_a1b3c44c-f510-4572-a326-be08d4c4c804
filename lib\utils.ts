import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Types for our trading bot application
export interface Bot {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  solPerTrade: number;
  stats: BotStats;
  config: BotConfig;
}

export interface BotConfig {
  stopLoss: number; // Percentage
  takeProfit: number; // Percentage
  maxSlippage: number; // Percentage
  minLiquidity: number; // SOL
  rsiThreshold?: {
    buy: number;
    sell: number;
  };
  emaCrossThreshold?: {
    fastPeriod: number;
    slowPeriod: number;
  };
  strategy:
    | "RSI"
    | "MOMENTUM"
    | "SCALPING"
    | "CUSTOM"
    | "AI_PROMPT"
    | "EMA_CROSS";
  timeframe: "1m" | "5m" | "15m" | "1h";
  maxPositions: number;
}

export interface BotStats {
  totalTrades: number;
  winRate: number;
  pnl: number;
  currentPosition?: Position;
}

export interface Position {
  token: string;
  amount: number;
  entryPrice: number;
  currentPrice: number;
  pnl: number;
  timestamp: Date;
}

export interface Trade {
  id: string;
  botId: string;
  type: "BUY" | "SELL";
  token: string;
  amount: number;
  price: number;
  pnl?: number;
  timestamp: Date;
  status: "PENDING" | "COMPLETED" | "FAILED";
}

export interface TradingPosition {
  id: string;
  botId: string;
  botName: string;
  token: string;
  tokenName?: string;
  tokenImage?: string;
  amount: number;
  entryPrice: number;
  currentPrice: number;
  pnl: number;
  pnlPercentage: number;
  timestamp: Date;
  status: "ACTIVE" | "PENDING_SELL";
  stopLoss?: number;
  takeProfit?: number;
}

export interface WatchingItem {
  id: string;
  botId: string;
  botName: string;
  token: string;
  tokenName?: string;
  tokenImage?: string;
  currentPrice: number;
  targetPrice?: number;
  indicators: {
    rsi?: number;
    macd?: string;
    signal?: "BUY" | "SELL" | "HOLD";
  };
  addedAt: Date;
  lastChecked: Date;
  strategy: string;
}

export interface ArchivedTrade {
  id: string;
  botId: string;
  botName: string;
  token: string;
  tokenName?: string;
  tokenImage?: string;
  entryPrice: number;
  exitPrice: number;
  amount: number;
  pnl: number;
  pnlPercentage: number;
  entryTime: Date;
  exitTime: Date;
  duration: number; // in minutes
  reason: "MANUAL" | "STOP_LOSS" | "TAKE_PROFIT" | "BOT_STOPPED";
}

// Utility functions
export function formatSOL(amount: number): string {
  return `${amount.toFixed(4)} SOL`;
}

export function formatUSD(amount: number): string {
  return `$${amount.toFixed(2)}`;
}

export function formatPercentage(value: number): string {
  const sign = value >= 0 ? "+" : "";
  return `${sign}${value.toFixed(2)}%`;
}
