"use client";

import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { Bot, Trade, BotConfig } from "@/lib/utils";
import { mockBots, mockTrades, generateRandomTrade } from "@/lib/mock-data";
import { CreateBotData } from "@/types/bot";
import { TradingEngine } from "@/lib/trading-engine";
import { toast } from "sonner";

interface BotContextType {
  bots: Bot[];
  trades: Trade[];
  customBots: Bot[];
  startBot: (botId: string, solPerTrade: number) => Promise<void>;
  stopBot: (botId: string) => Promise<void>;
  manualExit: (botId: string) => Promise<void>;
  updateBotSettings: (botId: string, settings: { solPerTrade: number }) => void;
  updateBotConfig: (botId: string, config: BotConfig) => void;
  createCustomBot: (botData: CreateBotData) => void;
  deleteCustomBot: (botId: string) => void;
  getBotById: (id: string) => Bot | undefined;
  getTradesForBot: (botId: string) => Trade[];
  getTradingEngine: (botId: string) => TradingEngine | undefined;
  isRealTradingEnabled: boolean;
  toggleRealTrading: () => void;
}

const BotContext = createContext<BotContextType | undefined>(undefined);

export function useBots() {
  const context = useContext(BotContext);
  if (!context) {
    throw new Error("useBots must be used within a BotProvider");
  }
  return context;
}

interface BotProviderProps {
  children: ReactNode;
}

export function BotProvider({ children }: BotProviderProps) {
  const [bots, setBots] = useState<Bot[]>(mockBots);
  const [customBots, setCustomBots] = useState<Bot[]>([]);
  const [trades, setTrades] = useState<Trade[]>(mockTrades);
  const [tradingEngines, setTradingEngines] = useState<
    Map<string, TradingEngine>
  >(new Map());
  const [isRealTradingEnabled, setIsRealTradingEnabled] =
    useState<boolean>(false);

  // Combine default and custom bots
  const allBots = [...bots, ...customBots];

  // Simulate real-time updates for active bots
  useEffect(() => {
    const interval = setInterval(() => {
      const updateBotsState = (botsArray: Bot[]) => {
        return botsArray.map((bot) => {
          if (!bot.isActive) return bot;

          const updatedStats = { ...bot.stats };

          // Update current position if exists
          if (updatedStats.currentPosition) {
            const priceChange = (Math.random() - 0.5) * 0.02; // ±1% change
            updatedStats.currentPosition.currentPrice *= 1 + priceChange;

            // Recalculate P&L
            const priceDiff =
              updatedStats.currentPosition.currentPrice -
              updatedStats.currentPosition.entryPrice;
            updatedStats.currentPosition.pnl =
              (priceDiff / updatedStats.currentPosition.entryPrice) *
              bot.solPerTrade;
          }

          // Randomly generate new trades for active bots
          if (Math.random() > 0.85) {
            // 15% chance every interval
            const newTrade = generateRandomTrade(bot.id);
            setTrades((prev) => [newTrade, ...prev].slice(0, 100)); // Keep last 100 trades

            // Show trade notification
            if (newTrade.pnl && newTrade.pnl > 0) {
              toast.success(
                `${bot.name}: Profitable ${newTrade.type} on ${
                  newTrade.token
                } (+${newTrade.pnl.toFixed(4)} SOL)`,
              );
            } else if (newTrade.pnl && newTrade.pnl < 0) {
              toast.error(
                `${bot.name}: Loss on ${newTrade.type} ${
                  newTrade.token
                } (${newTrade.pnl.toFixed(4)} SOL)`,
              );
            }

            // Update bot stats
            updatedStats.totalTrades += 1;
            if (newTrade.pnl) {
              updatedStats.pnl += newTrade.pnl;

              // Update win rate
              const wins = Math.floor(
                updatedStats.totalTrades * (updatedStats.winRate / 100),
              );
              const newWins = newTrade.pnl > 0 ? wins + 1 : wins;
              updatedStats.winRate = (newWins / updatedStats.totalTrades) * 100;
            }

            // Randomly create or close positions
            if (!updatedStats.currentPosition && newTrade.type === "BUY") {
              updatedStats.currentPosition = {
                token: newTrade.token,
                amount: newTrade.amount,
                entryPrice: newTrade.price,
                currentPrice: newTrade.price,
                pnl: 0,
                timestamp: newTrade.timestamp,
              };
            } else if (
              updatedStats.currentPosition &&
              newTrade.type === "SELL" &&
              updatedStats.currentPosition.token === newTrade.token
            ) {
              updatedStats.currentPosition = undefined;
            }
          }

          return { ...bot, stats: updatedStats };
        });
      };

      setBots(updateBotsState);
      setCustomBots(updateBotsState);
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, []);

  const startBot = async (
    botId: string,
    solPerTrade: number,
  ): Promise<void> => {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const bot = allBots.find((b) => b.id === botId);
    if (!bot) return;

    const updateBot = (bot: Bot) =>
      bot.id === botId ? { ...bot, isActive: true, solPerTrade } : bot;

    setBots((prevBots) => prevBots.map(updateBot));
    setCustomBots((prevBots) => prevBots.map(updateBot));

    // Start real trading engine if enabled
    if (isRealTradingEnabled) {
      const engine = new TradingEngine(bot);

      // Subscribe to trade events
      engine.onTradeExecuted((trade: Trade) => {
        setTrades((prev) => [trade, ...prev].slice(0, 100));

        // Show trade notification
        if (trade.pnl && trade.pnl > 0) {
          toast.success(
            `${bot.name}: Profitable ${trade.type} on ${
              trade.token
            } (+${trade.pnl.toFixed(4)} SOL)`,
          );
        } else if (trade.pnl && trade.pnl < 0) {
          toast.error(
            `${bot.name}: Loss on ${trade.type} ${
              trade.token
            } (${trade.pnl.toFixed(4)} SOL)`,
          );
        }
      });

      engine.start();
      setTradingEngines((prev) => new Map(prev).set(botId, engine));

      toast.success(`${bot.name} started with real trading engine!`);
    } else {
      toast.success(`${bot.name} started in simulation mode`);
    }
  };

  const stopBot = async (botId: string): Promise<void> => {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const updateBot = (bot: Bot) =>
      bot.id === botId ? { ...bot, isActive: false } : bot;

    setBots((prevBots) => prevBots.map(updateBot));
    setCustomBots((prevBots) => prevBots.map(updateBot));

    // Stop trading engine if running
    const engine = tradingEngines.get(botId);
    if (engine) {
      engine.stop();
      setTradingEngines((prev) => {
        const newMap = new Map(prev);
        newMap.delete(botId);
        return newMap;
      });

      const bot = allBots.find((b) => b.id === botId);
      toast.success(`${bot?.name || "Bot"} stopped`);
    }
  };

  const manualExit = async (botId: string): Promise<void> => {
    const bot = allBots.find((b) => b.id === botId);
    if (!bot?.stats.currentPosition) return;

    // Create exit trade
    const exitTrade: Trade = {
      id: Math.random().toString(36).substr(2, 9),
      botId,
      type: "SELL",
      token: bot.stats.currentPosition.token,
      amount: bot.stats.currentPosition.amount,
      price: bot.stats.currentPosition.currentPrice,
      pnl: bot.stats.currentPosition.pnl,
      timestamp: new Date(),
      status: "COMPLETED",
    };

    setTrades((prev) => [exitTrade, ...prev]);

    // Clear current position and update stats
    const updateBot = (b: Bot) =>
      b.id === botId
        ? {
            ...b,
            stats: {
              ...b.stats,
              currentPosition: undefined,
              totalTrades: b.stats.totalTrades + 1,
              pnl: b.stats.pnl + (bot.stats.currentPosition?.pnl || 0),
            },
          }
        : b;

    setBots((prevBots) => prevBots.map(updateBot));
    setCustomBots((prevBots) => prevBots.map(updateBot));
  };

  const updateBotSettings = (
    botId: string,
    settings: { solPerTrade: number },
  ) => {
    const updateBot = (bot: Bot) =>
      bot.id === botId ? { ...bot, solPerTrade: settings.solPerTrade } : bot;

    setBots((prevBots) => prevBots.map(updateBot));
    setCustomBots((prevBots) => prevBots.map(updateBot));
  };

  const updateBotConfig = (botId: string, config: BotConfig) => {
    const updateBot = (bot: Bot) =>
      bot.id === botId ? { ...bot, config } : bot;

    setBots((prevBots) => prevBots.map(updateBot));
    setCustomBots((prevBots) => prevBots.map(updateBot));
  };

  const createCustomBot = (botData: CreateBotData) => {
    const newBot: Bot = {
      id: `custom-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: botData.name,
      description: botData.description,
      isActive: false,
      solPerTrade: botData.solPerTrade,
      config: botData.config,
      stats: {
        totalTrades: 0,
        winRate: 0,
        pnl: 0,
      },
    };

    setCustomBots((prev) => [...prev, newBot]);
  };

  const deleteCustomBot = (botId: string) => {
    setCustomBots((prev) => prev.filter((bot) => bot.id !== botId));
    // Also remove any trades for this bot
    setTrades((prev) => prev.filter((trade) => trade.botId !== botId));
  };

  const getBotById = (id: string): Bot | undefined => {
    return allBots.find((bot) => bot.id === id);
  };

  const getTradesForBot = (botId: string): Trade[] => {
    return trades.filter((trade) => trade.botId === botId);
  };

  const getTradingEngine = (botId: string): TradingEngine | undefined => {
    return tradingEngines.get(botId);
  };

  const toggleRealTrading = () => {
    setIsRealTradingEnabled((prev) => !prev);
    toast.success(
      `Real trading ${!isRealTradingEnabled ? "enabled" : "disabled"}`,
    );
  };

  const value: BotContextType = {
    bots: allBots,
    trades,
    customBots,
    startBot,
    stopBot,
    manualExit,
    updateBotSettings,
    updateBotConfig,
    createCustomBot,
    deleteCustomBot,
    getBotById,
    getTradesForBot,
    getTradingEngine,
    isRealTradingEnabled,
    toggleRealTrading,
  };

  return <BotContext.Provider value={value}>{children}</BotContext.Provider>;
}
