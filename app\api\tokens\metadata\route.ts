import { NextRequest, NextResponse } from "next/server";
import { DexScreenerService } from "@/lib/dexscreener-service";

const dexScreenerService = new DexScreenerService();

// Token symbol mappings for better search results
const TOKEN_MAPPINGS: Record<string, string> = {
  BCH: "bitcoin-cash",
  AVAX: "avalanche-2",
  WEETH: "wrapped-eeth",
  BTC: "bitcoin",
  ETH: "ethereum",
  SOL: "solana",
  USDC: "usd-coin",
  USDT: "tether",
};

// Fallback to CoinGecko for major tokens
async function fetchFromCoinGecko(symbol: string) {
  try {
    const coinId = TOKEN_MAPPINGS[symbol.toUpperCase()] || symbol.toLowerCase();

    // First try to get coin data by ID
    const response = await fetch(
      `https://api.coingecko.com/api/v3/coins/${coinId}?localization=false&tickers=false&market_data=true&community_data=false&developer_data=false&sparkline=false`,
      {
        headers: {
          Accept: "application/json",
          "User-Agent": "DexTrip/1.0",
        },
      },
    );

    if (response.ok) {
      const data = await response.json();
      return {
        name: data.name,
        symbol: data.symbol?.toUpperCase(),
        image_url:
          data.image?.large ||
          data.image?.small ||
          `https://api.dicebear.com/7.x/identicon/svg?seed=${symbol}`,
        price: data.market_data?.current_price?.usd || 0,
        price_change_24h: data.market_data?.price_change_percentage_24h || 0,
        volume_24h: data.market_data?.total_volume?.usd || 0,
        market_cap: data.market_data?.market_cap?.usd || 0,
        liquidity: 0,
        address: "",
        url: `https://www.coingecko.com/en/coins/${coinId}`,
      };
    }

    // If direct lookup fails, try search
    const searchResponse = await fetch(
      `https://api.coingecko.com/api/v3/search?query=${encodeURIComponent(symbol)}`,
      {
        headers: {
          Accept: "application/json",
          "User-Agent": "DexTrip/1.0",
        },
      },
    );

    if (searchResponse.ok) {
      const searchData = await searchResponse.json();
      if (searchData.coins && searchData.coins.length > 0) {
        const coin = searchData.coins[0];
        return {
          name: coin.name,
          symbol: coin.symbol?.toUpperCase(),
          image_url:
            coin.large ||
            coin.thumb ||
            `https://api.dicebear.com/7.x/identicon/svg?seed=${symbol}`,
          price: 0, // Search doesn't include price data
          price_change_24h: 0,
          volume_24h: 0,
          market_cap: 0,
          liquidity: 0,
          address: "",
          url: `https://www.coingecko.com/en/coins/${coin.id}`,
        };
      }
    }

    return null;
  } catch (error) {
    console.error("CoinGecko API error:", error);
    return null;
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const symbol = searchParams.get("symbol");

    if (!symbol) {
      return NextResponse.json(
        { success: false, error: "Symbol parameter is required" },
        { status: 400 },
      );
    }

    // First try DexScreener for DEX tokens
    const tokens = await dexScreenerService.searchTokens(symbol);

    if (tokens.length > 0) {
      // Get the best match (first result)
      const token = tokens[0];

      const metadata = {
        name: token.baseToken?.name || symbol,
        symbol: token.baseToken?.symbol || symbol,
        image_url:
          token.info?.imageUrl ||
          `https://api.dicebear.com/7.x/identicon/svg?seed=${symbol}`,
        price: token.priceUsd ? parseFloat(token.priceUsd) : 0,
        price_change_24h: token.priceChange?.h24 || 0,
        volume_24h: token.volume?.h24 || 0,
        market_cap: token.marketCap || 0,
        liquidity: token.liquidity?.usd || 0,
        address: token.baseToken?.address || "",
        url:
          token.url ||
          `https://dexscreener.com/solana/${token.baseToken?.address}`,
      };

      return NextResponse.json({
        success: true,
        token: metadata,
        source: "dexscreener",
      });
    }

    // If DexScreener doesn't have it, try CoinGecko
    console.log(`⚠️ No DexScreener data for ${symbol}, trying CoinGecko...`);
    const coinGeckoData = await fetchFromCoinGecko(symbol);

    if (coinGeckoData) {
      return NextResponse.json({
        success: true,
        token: coinGeckoData,
        source: "coingecko",
      });
    }

    // If both fail, return a basic response with fallback data
    console.log(`⚠️ No data found for ${symbol}, using fallback`);
    const fallbackMetadata = {
      name: symbol.toUpperCase(),
      symbol: symbol.toUpperCase(),
      image_url: `https://api.dicebear.com/7.x/identicon/svg?seed=${symbol}`,
      price: 0,
      price_change_24h: 0,
      volume_24h: 0,
      market_cap: 0,
      liquidity: 0,
      address: "",
      url: `https://www.coingecko.com/en/search?query=${encodeURIComponent(symbol)}`,
    };

    return NextResponse.json({
      success: true,
      token: fallbackMetadata,
      source: "fallback",
    });
  } catch (error) {
    console.error("Error fetching token metadata:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch token metadata" },
      { status: 500 },
    );
  }
}
