"use client";

import React, { useState, useEffect } from "react";
import {
  Bread<PERSON>rumb,
  BreadcrumbI<PERSON>,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbLink,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import {
  TrendingUp,
  TrendingDown,
  Plus,
  Trash2,
  DollarSign,
  PieChart,
  RefreshCw,
  Wallet,
} from "lucide-react";
import Image from "next/image";

interface PortfolioHolding {
  id: string;
  symbol: string;
  name: string;
  image: string;
  amount: number;
  averagePrice: number;
  currentPrice: number;
  totalValue: number;
  pnl: number;
  pnlPercentage: number;
  lastUpdated: Date;
}

interface PortfolioStats {
  totalValue: number;
  totalPnL: number;
  totalPnLPercentage: number;
  topGainer: PortfolioHolding | null;
  topLoser: PortfolioHolding | null;
}

export default function PortfolioPage() {
  const [holdings, setHoldings] = useState<PortfolioHolding[]>([]);
  const [stats, setStats] = useState<PortfolioStats>({
    totalValue: 0,
    totalPnL: 0,
    totalPnLPercentage: 0,
    topGainer: null,
    topLoser: null,
  });
  const [loading, setLoading] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);

  // Add holding form state
  const [newHolding, setNewHolding] = useState({
    symbol: "",
    amount: "",
    averagePrice: "",
  });

  const formatPrice = (price: number) => {
    if (price < 0.01) {
      return `$${price.toFixed(6)}`;
    } else if (price < 1) {
      return `$${price.toFixed(4)}`;
    } else {
      return `$${price.toFixed(2)}`;
    }
  };



  const calculateStats = (holdings: PortfolioHolding[]): PortfolioStats => {
    const totalValue = holdings.reduce((sum, holding) => sum + holding.totalValue, 0);
    const totalPnL = holdings.reduce((sum, holding) => sum + holding.pnl, 0);
    const totalPnLPercentage = totalValue > 0 ? (totalPnL / (totalValue - totalPnL)) * 100 : 0;

    const topGainer = holdings.reduce((max, holding) => 
      !max || holding.pnlPercentage > max.pnlPercentage ? holding : max, null as PortfolioHolding | null);
    
    const topLoser = holdings.reduce((min, holding) => 
      !min || holding.pnlPercentage < min.pnlPercentage ? holding : min, null as PortfolioHolding | null);

    return {
      totalValue,
      totalPnL,
      totalPnLPercentage,
      topGainer,
      topLoser,
    };
  };

  const fetchCurrentPrices = async () => {
    if (holdings.length === 0) return;

    setLoading(true);
    try {
      // This would typically call your price API
      // For now, we'll simulate with random price updates
      const updatedHoldings = holdings.map(holding => {
        // Simulate price changes (±5%)
        const priceChange = (Math.random() - 0.5) * 0.1;
        const currentPrice = holding.currentPrice * (1 + priceChange);
        const totalValue = holding.amount * currentPrice;
        const pnl = totalValue - (holding.amount * holding.averagePrice);
        const pnlPercentage = ((currentPrice - holding.averagePrice) / holding.averagePrice) * 100;

        return {
          ...holding,
          currentPrice,
          totalValue,
          pnl,
          pnlPercentage,
          lastUpdated: new Date(),
        };
      });

      setHoldings(updatedHoldings);
      setStats(calculateStats(updatedHoldings));
    } catch (error) {
      console.error("Error fetching prices:", error);
    } finally {
      setLoading(false);
    }
  };

  const addHolding = () => {
    if (!newHolding.symbol || !newHolding.amount || !newHolding.averagePrice) {
      return;
    }

    const amount = parseFloat(newHolding.amount);
    const averagePrice = parseFloat(newHolding.averagePrice);
    const currentPrice = averagePrice; // Start with average price
    const totalValue = amount * currentPrice;

    const holding: PortfolioHolding = {
      id: Date.now().toString(),
      symbol: newHolding.symbol.toUpperCase(),
      name: newHolding.symbol.toUpperCase(), // Would fetch from API
      image: `https://api.dicebear.com/7.x/identicon/svg?seed=${newHolding.symbol}`,
      amount,
      averagePrice,
      currentPrice,
      totalValue,
      pnl: 0,
      pnlPercentage: 0,
      lastUpdated: new Date(),
    };

    const updatedHoldings = [...holdings, holding];
    setHoldings(updatedHoldings);
    setStats(calculateStats(updatedHoldings));
    setNewHolding({ symbol: "", amount: "", averagePrice: "" });
    setShowAddForm(false);
  };

  const removeHolding = (id: string) => {
    const updatedHoldings = holdings.filter(h => h.id !== id);
    setHoldings(updatedHoldings);
    setStats(calculateStats(updatedHoldings));
  };

  useEffect(() => {
    // Load sample data
    const sampleHoldings: PortfolioHolding[] = [
      {
        id: "1",
        symbol: "BTC",
        name: "Bitcoin",
        image: "https://assets.coingecko.com/coins/images/1/large/bitcoin.png",
        amount: 0.5,
        averagePrice: 45000,
        currentPrice: 47500,
        totalValue: 23750,
        pnl: 1250,
        pnlPercentage: 5.56,
        lastUpdated: new Date(),
      },
      {
        id: "2",
        symbol: "ETH",
        name: "Ethereum",
        image: "https://assets.coingecko.com/coins/images/279/large/ethereum.png",
        amount: 2.5,
        averagePrice: 2800,
        currentPrice: 2650,
        totalValue: 6625,
        pnl: -375,
        pnlPercentage: -5.36,
        lastUpdated: new Date(),
      },
      {
        id: "3",
        symbol: "SOL",
        name: "Solana",
        image: "https://assets.coingecko.com/coins/images/4128/large/solana.png",
        amount: 10,
        averagePrice: 120,
        currentPrice: 135,
        totalValue: 1350,
        pnl: 150,
        pnlPercentage: 12.5,
        lastUpdated: new Date(),
      },
    ];

    setHoldings(sampleHoldings);
    setStats(calculateStats(sampleHoldings));
  }, []);

  return (
    <>
      <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className="hidden md:block" />
              <BreadcrumbItem>
                <BreadcrumbPage>Portfolio</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min">
          <div className="p-6">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
                  <Wallet className="h-8 w-8" />
                  Portfolio
                </h1>
                <p className="text-muted-foreground">
                  Track your cryptocurrency holdings and performance
                </p>
              </div>
              <div className="flex gap-2">
                <Button onClick={fetchCurrentPrices} variant="outline" disabled={loading}>
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  Refresh Prices
                </Button>
                <Button onClick={() => setShowAddForm(!showAddForm)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Holding
                </Button>
              </div>
            </div>

            {/* Portfolio Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Value</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatPrice(stats.totalValue)}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total P&L</CardTitle>
                  {stats.totalPnL >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-600" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-600" />
                  )}
                </CardHeader>
                <CardContent>
                  <div className={`text-2xl font-bold ${stats.totalPnL >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {stats.totalPnL >= 0 ? '+' : ''}{formatPrice(stats.totalPnL)}
                  </div>
                  <p className={`text-xs ${stats.totalPnL >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {stats.totalPnLPercentage >= 0 ? '+' : ''}{stats.totalPnLPercentage.toFixed(2)}%
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Top Gainer</CardTitle>
                  <TrendingUp className="h-4 w-4 text-green-600" />
                </CardHeader>
                <CardContent>
                  {stats.topGainer ? (
                    <>
                      <div className="text-lg font-bold">{stats.topGainer.symbol}</div>
                      <p className="text-xs text-green-600">
                        +{stats.topGainer.pnlPercentage.toFixed(2)}%
                      </p>
                    </>
                  ) : (
                    <div className="text-sm text-muted-foreground">No holdings</div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Top Loser</CardTitle>
                  <TrendingDown className="h-4 w-4 text-red-600" />
                </CardHeader>
                <CardContent>
                  {stats.topLoser ? (
                    <>
                      <div className="text-lg font-bold">{stats.topLoser.symbol}</div>
                      <p className="text-xs text-red-600">
                        {stats.topLoser.pnlPercentage.toFixed(2)}%
                      </p>
                    </>
                  ) : (
                    <div className="text-sm text-muted-foreground">No holdings</div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Add Holding Form */}
            {showAddForm && (
              <Card className="mb-6">
                <CardHeader>
                  <CardTitle>Add New Holding</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Input
                      placeholder="Symbol (e.g., BTC)"
                      value={newHolding.symbol}
                      onChange={(e) => setNewHolding({ ...newHolding, symbol: e.target.value })}
                    />
                    <Input
                      placeholder="Amount"
                      type="number"
                      value={newHolding.amount}
                      onChange={(e) => setNewHolding({ ...newHolding, amount: e.target.value })}
                    />
                    <Input
                      placeholder="Average Price"
                      type="number"
                      value={newHolding.averagePrice}
                      onChange={(e) => setNewHolding({ ...newHolding, averagePrice: e.target.value })}
                    />
                    <div className="flex gap-2">
                      <Button onClick={addHolding} className="flex-1">
                        Add
                      </Button>
                      <Button onClick={() => setShowAddForm(false)} variant="outline">
                        Cancel
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Holdings Table */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Holdings
                  <Badge variant="secondary">{holdings.length}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {holdings.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground mb-4">No holdings yet</p>
                    <Button onClick={() => setShowAddForm(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Your First Holding
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {holdings.map((holding) => (
                      <div
                        key={holding.id}
                        className="flex items-center gap-4 p-4 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                      >
                        <Image
                          src={holding.image}
                          alt={holding.name}
                          width={40}
                          height={40}
                          className="rounded-full"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = `https://api.dicebear.com/7.x/identicon/svg?seed=${holding.symbol}`;
                          }}
                        />
                        
                        <div className="flex-1">
                          <div className="font-medium">{holding.name}</div>
                          <div className="text-sm text-muted-foreground">{holding.symbol}</div>
                        </div>

                        <div className="text-right">
                          <div className="font-medium">{holding.amount}</div>
                          <div className="text-sm text-muted-foreground">Amount</div>
                        </div>

                        <div className="text-right">
                          <div className="font-medium">{formatPrice(holding.averagePrice)}</div>
                          <div className="text-sm text-muted-foreground">Avg Price</div>
                        </div>

                        <div className="text-right">
                          <div className="font-medium">{formatPrice(holding.currentPrice)}</div>
                          <div className="text-sm text-muted-foreground">Current</div>
                        </div>

                        <div className="text-right">
                          <div className="font-medium">{formatPrice(holding.totalValue)}</div>
                          <div className="text-sm text-muted-foreground">Value</div>
                        </div>

                        <div className="text-right">
                          <div className={`font-medium ${holding.pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {holding.pnl >= 0 ? '+' : ''}{formatPrice(holding.pnl)}
                          </div>
                          <div className={`text-sm ${holding.pnlPercentage >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {holding.pnlPercentage >= 0 ? '+' : ''}{holding.pnlPercentage.toFixed(2)}%
                          </div>
                        </div>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeHolding(holding.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </>
  );
}
