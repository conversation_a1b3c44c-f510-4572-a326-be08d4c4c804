"use client";

import { useState } from "react";
import { Filter } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";

export interface SimpleFilters {
  minPrice?: number;
  maxPrice?: number;
  minVolume?: number;
  maxAge?: number; // in hours
  dexId?: string;
}

interface SimpleFiltersProps {
  filters: SimpleFilters;
  onFiltersChange: (filters: SimpleFilters) => void;
  chain: "solana" | "ethereum";
}

export function SimpleFiltersComponent({
  filters,
  onFiltersChange,
  chain,
}: SimpleFiltersProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [tempFilters, setTempFilters] = useState<SimpleFilters>(filters);

  const handleApplyFilters = () => {
    onFiltersChange(tempFilters);
    setIsOpen(false);
  };

  const handleResetFilters = () => {
    const resetFilters: SimpleFilters = {};
    setTempFilters(resetFilters);
    onFiltersChange(resetFilters);
  };

  const getActiveFiltersCount = () => {
    return Object.keys(filters).filter((key) => {
      const value = filters[key as keyof SimpleFilters];
      return value !== undefined && value !== null && value !== "";
    }).length;
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <Filter className="h-4 w-4" />
          Filters
          {getActiveFiltersCount() > 0 && (
            <Badge variant="secondary" className="ml-1 h-5 w-5 p-0 text-xs">
              {getActiveFiltersCount()}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-4" align="start">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">Filters</h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleResetFilters}
              className="h-auto p-1 text-xs">
              Reset
            </Button>
          </div>

          {/* Price Range */}
          <div className="space-y-2">
            <Label className="text-sm">Price Range (USD)</Label>
            <div className="grid grid-cols-2 gap-2">
              <Input
                type="number"
                placeholder="Min"
                value={tempFilters.minPrice || ""}
                onChange={(e) =>
                  setTempFilters({
                    ...tempFilters,
                    minPrice: e.target.value
                      ? parseFloat(e.target.value)
                      : undefined,
                  })
                }
                className="h-8"
              />
              <Input
                type="number"
                placeholder="Max"
                value={tempFilters.maxPrice || ""}
                onChange={(e) =>
                  setTempFilters({
                    ...tempFilters,
                    maxPrice: e.target.value
                      ? parseFloat(e.target.value)
                      : undefined,
                  })
                }
                className="h-8"
              />
            </div>
          </div>

          {/* Volume Filter */}
          <div className="space-y-2">
            <Label className="text-sm">Min Volume (24h USD)</Label>
            <Input
              type="number"
              placeholder="e.g., 10000"
              value={tempFilters.minVolume || ""}
              onChange={(e) =>
                setTempFilters({
                  ...tempFilters,
                  minVolume: e.target.value
                    ? parseFloat(e.target.value)
                    : undefined,
                })
              }
              className="h-8"
            />
          </div>

          {/* Age Filter */}
          <div className="space-y-2">
            <Label className="text-sm">Max Age (hours)</Label>
            <Input
              type="number"
              placeholder="24"
              value={tempFilters.maxAge || ""}
              onChange={(e) =>
                setTempFilters({
                  ...tempFilters,
                  maxAge: e.target.value
                    ? parseFloat(e.target.value)
                    : undefined,
                })
              }
              className="h-8"
            />
          </div>

          {/* DEX Filter */}
          <div className="space-y-2">
            <Label className="text-sm">Exchange</Label>
            <div className="flex flex-wrap gap-1">
              {chain === "solana" ? (
                <>
                  <Button
                    variant={
                      tempFilters.dexId === "pumpfun" ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() =>
                      setTempFilters({
                        ...tempFilters,
                        dexId:
                          tempFilters.dexId === "pumpfun"
                            ? undefined
                            : "pumpfun",
                      })
                    }
                    className="h-7 text-xs">
                    Pump.fun
                  </Button>
                  <Button
                    variant={
                      tempFilters.dexId === "raydium" ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() =>
                      setTempFilters({
                        ...tempFilters,
                        dexId:
                          tempFilters.dexId === "raydium"
                            ? undefined
                            : "raydium",
                      })
                    }
                    className="h-7 text-xs">
                    Raydium
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    variant={
                      tempFilters.dexId === "uniswap-v2" ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() =>
                      setTempFilters({
                        ...tempFilters,
                        dexId:
                          tempFilters.dexId === "uniswap-v2"
                            ? undefined
                            : "uniswap-v2",
                      })
                    }
                    className="h-7 text-xs">
                    Uniswap V2
                  </Button>
                  <Button
                    variant={
                      tempFilters.dexId === "uniswap-v3" ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() =>
                      setTempFilters({
                        ...tempFilters,
                        dexId:
                          tempFilters.dexId === "uniswap-v3"
                            ? undefined
                            : "uniswap-v3",
                      })
                    }
                    className="h-7 text-xs">
                    Uniswap V3
                  </Button>
                </>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-2">
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              className="flex-1 h-8">
              Cancel
            </Button>
            <Button onClick={handleApplyFilters} className="flex-1 h-8">
              Apply
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
