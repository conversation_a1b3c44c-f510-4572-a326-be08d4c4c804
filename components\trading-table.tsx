"use client";

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  ArrowUpDown,
  TrendingUp,
  TrendingDown,
  StopCircle,
} from "lucide-react";
import { motion } from "framer-motion";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { TradingPosition } from "@/lib/utils";
import { useBots } from "@/contexts/bot-context";
import { formatSOL, formatUSD } from "@/lib/utils";
import { toast } from "sonner";

export function TradingTable() {
  const { bots, manualExit } = useBots();
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});

  // Convert active bot positions to trading positions
  const tradingPositions: TradingPosition[] = React.useMemo(() => {
    return bots
      .filter((bot) => bot.isActive && bot.stats.currentPosition)
      .map((bot) => {
        const position = bot.stats.currentPosition!;
        const pnlPercentage =
          (position.pnl / (position.entryPrice * position.amount)) * 100;

        return {
          id: `${bot.id}-${position.token}`,
          botId: bot.id,
          botName: bot.name,
          token: position.token,
          tokenName: position.token,
          tokenImage: `/api/placeholder/32/32`, // Placeholder for now
          amount: position.amount,
          entryPrice: position.entryPrice,
          currentPrice: position.currentPrice,
          pnl: position.pnl,
          pnlPercentage,
          timestamp: position.timestamp,
          status: "ACTIVE" as const,
          stopLoss: bot.config.stopLoss
            ? position.entryPrice * (1 - bot.config.stopLoss / 100)
            : undefined,
          takeProfit: bot.config.takeProfit
            ? position.entryPrice * (1 + bot.config.takeProfit / 100)
            : undefined,
        };
      });
  }, [bots]);

  const handleManualSell = async (position: TradingPosition) => {
    try {
      await manualExit(position.botId);
      toast.success(`Successfully sold ${position.token} position`);
    } catch {
      toast.error(`Failed to sell ${position.token} position`);
    }
  };

  const formatTime = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);

    if (diffMins < 60) {
      return `${diffMins}m ago`;
    } else if (diffHours < 24) {
      return `${diffHours}h ago`;
    } else {
      const diffDays = Math.floor(diffHours / 24);
      return `${diffDays}d ago`;
    }
  };

  const columns: ColumnDef<TradingPosition>[] = [
    {
      accessorKey: "token",
      header: "Token",
      cell: ({ row }) => {
        const position = row.original;
        return (
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white text-sm font-bold">
              {position.token.slice(0, 2).toUpperCase()}
            </div>
            <div>
              <div className="font-medium">{position.token}</div>
              <div className="text-sm text-muted-foreground">
                {position.tokenName}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "botName",
      header: "Bot",
      cell: ({ row }) => {
        const position = row.original;
        return (
          <Badge variant="outline" className="font-mono">
            {position.botName}
          </Badge>
        );
      },
    },
    {
      accessorKey: "amount",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Amount
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const position = row.original;
        return (
          <div className="font-mono">{position.amount.toLocaleString()}</div>
        );
      },
    },
    {
      accessorKey: "entryPrice",
      header: "Entry Price",
      cell: ({ row }) => {
        const position = row.original;
        return (
          <div className="font-mono">{formatUSD(position.entryPrice)}</div>
        );
      },
    },
    {
      accessorKey: "currentPrice",
      header: "Current Price",
      cell: ({ row }) => {
        const position = row.original;
        const priceChange =
          ((position.currentPrice - position.entryPrice) /
            position.entryPrice) *
          100;
        return (
          <div className="flex items-center gap-2">
            <span className="font-mono">
              {formatUSD(position.currentPrice)}
            </span>
            <div
              className={`flex items-center gap-1 text-sm ${
                priceChange >= 0 ? "text-green-500" : "text-red-500"
              }`}
            >
              {priceChange >= 0 ? (
                <TrendingUp className="h-3 w-3" />
              ) : (
                <TrendingDown className="h-3 w-3" />
              )}
              {Math.abs(priceChange).toFixed(2)}%
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "pnl",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            P&L
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const position = row.original;
        return (
          <div
            className={`font-mono ${
              position.pnl >= 0 ? "text-green-500" : "text-red-500"
            }`}
          >
            <div>{formatSOL(position.pnl)}</div>
            <div className="text-sm">
              {position.pnlPercentage >= 0 ? "+" : ""}
              {position.pnlPercentage.toFixed(2)}%
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "timestamp",
      header: "Duration",
      cell: ({ row }) => {
        const position = row.original;
        return (
          <div className="text-sm text-muted-foreground">
            {formatTime(position.timestamp)}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const position = row.original;
        return (
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="destructive"
              onClick={() => handleManualSell(position)}
              className="flex items-center gap-1"
            >
              <StopCircle className="h-3 w-3" />
              Sell
            </Button>
            {/* <Button
              size="sm"
              variant="outline"
              className="flex items-center gap-1">
              <Settings className="h-3 w-3" />
            </Button> */}
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data: tradingPositions,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Active Trading Positions
          <Badge variant="secondary">{tradingPositions.length}</Badge>
          <div className="flex items-center gap-1 ml-auto">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-xs text-muted-foreground">Live</span>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center py-4">
          <Input
            placeholder="Filter tokens..."
            value={(table.getColumn("token")?.getFilterValue() as string) ?? ""}
            onChange={(event) =>
              table.getColumn("token")?.setFilterValue(event.target.value)
            }
            className="max-w-sm"
          />
        </div>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row, index) => (
                  <motion.tr
                    key={row.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors"
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                  </motion.tr>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    No active trading positions.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
