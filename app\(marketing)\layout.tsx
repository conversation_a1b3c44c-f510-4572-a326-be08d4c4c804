"use client";

import { Navigation } from "@/components/navigation";
import { SignedIn, SignedOut } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useEffect, ReactNode } from "react";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";

const SignedInContent = ({ router }: { router: AppRouterInstance }) => {
  useEffect(() => {
    router.push("/bot");
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-[60vh]">
      <div className="animate-pulse">Redirecting to your dashboard...</div>
    </div>
  );
};

function MarketingLayout({ children }: { children: ReactNode }) {
  const router = useRouter();

  return (
    <div className="min-h-screen mx-auto flex flex-col">
      <Navigation />
      <SignedIn>
        <SignedInContent router={router} />
      </SignedIn>

      <SignedOut>
        <main className="flex-1">{children}</main>
      </SignedOut>
    </div>
  );
}

export default MarketingLayout;
