"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { TradingTable } from "@/components/trading-table";
import { WatchingTable } from "@/components/watching-table";
import { ArchiveTable } from "@/components/archive-table";
import { useBots } from "@/contexts/bot-context";
import { Eye, Archive, TrendingUp } from "lucide-react";

export default function TradesPage() {
  const { bots, trades } = useBots();

  // Count active positions, watching items, and archived trades
  const activeTrades = bots.filter(
    (bot) => bot.isActive && bot.stats.currentPosition,
  ).length;
  const watchingCount = bots.filter((bot) => !bot.isActive).length;
  const archivedCount = trades.filter(
    (trade) => trade.status === "COMPLETED",
  ).length;

  return (
    <div>
      <header className="flex h-16 shrink-0 items-center gap-2 justify-between">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbPage>TRADES</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
        <div className="text-sm text-muted-foreground font-bold pr-6">
          4,342 COINS TRADED
        </div>
      </header>
      <div className="h-px bg-border" />

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex-1 p-6">
        <Tabs defaultValue="trading" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="trading" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Trading
              {activeTrades > 0 && (
                <Badge variant="secondary" className="ml-1">
                  {activeTrades}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="watching" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Watching
              {watchingCount > 0 && (
                <Badge variant="secondary" className="ml-1">
                  {watchingCount}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="archive" className="flex items-center gap-2">
              <Archive className="h-4 w-4" />
              Archive
              {archivedCount > 0 && (
                <Badge variant="secondary" className="ml-1">
                  {archivedCount}
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="trading" className="mt-6">
            <TradingTable />
          </TabsContent>

          <TabsContent value="watching" className="mt-6">
            <WatchingTable />
          </TabsContent>

          <TabsContent value="archive" className="mt-6">
            <ArchiveTable />
          </TabsContent>
        </Tabs>
      </motion.div>
    </div>
  );
}
