import { Bot, Trade } from "./utils";

// Note: If you're getting 403 errors from Solana RPC:
// 1. The app is now configured to use devnet to avoid rate limits
// 2. For production, consider using a dedicated RPC provider like Helius, QuickNode, or Alchemy
// 3. Set NEXT_PUBLIC_RPC_URL in .env.local with your custom RPC endpoint

// Mock bot data
export const mockBots: Bot[] = [
  {
    id: "rsi-bot",
    name: "RSI Bot",
    description: "Trades based on RSI indicators with 70/30 thresholds",
    isActive: true,
    solPerTrade: 0.1,
    stats: {
      totalTrades: 47,
      winRate: 68.5,
      pnl: 2.34,
      currentPosition: {
        token: "BONK",
        amount: 1000000,
        entryPrice: 0.000012,
        currentPrice: 0.000014,
        pnl: 0.23,
        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
      },
    },
    config: {
      stopLoss: 5,
      takeProfit: 15,
      maxSlippage: 2,
      minLiquidity: 10,
      rsiThreshold: {
        buy: 30,
        sell: 70,
      },
      strategy: "RSI",
      timeframe: "5m",
      maxPositions: 3,
    },
  },
  {
    id: "pump-bot",
    name: "PumpBot",
    description: "Detects early pump signals and momentum trades",
    isActive: true,
    solPerTrade: 0.25,
    stats: {
      totalTrades: 23,
      winRate: 78.3,
      pnl: 4.67,
      currentPosition: {
        token: "WIF",
        amount: 100,
        entryPrice: 2.45,
        currentPrice: 2.67,
        pnl: 0.22,
        timestamp: new Date(Date.now() - 1000 * 60 * 45), // 45 minutes ago
      },
    },
    config: {
      stopLoss: 8,
      takeProfit: 25,
      maxSlippage: 3,
      minLiquidity: 20,
      strategy: "MOMENTUM",
      timeframe: "1m",
      maxPositions: 5,
    },
  },
  {
    id: "scalp-bot",
    name: "Scalp Master",
    description: "High-frequency scalping with tight stop losses",
    isActive: false,
    solPerTrade: 0.05,
    stats: {
      totalTrades: 156,
      winRate: 64.7,
      pnl: 1.89,
    },
    config: {
      stopLoss: 2,
      takeProfit: 5,
      maxSlippage: 0.5,
      minLiquidity: 5,
      strategy: "SCALPING",
      timeframe: "1m",
      maxPositions: 10,
    },
  },
];

// Mock trade history
export const mockTrades: Trade[] = [
  {
    id: "1",
    botId: "rsi-bot",
    type: "BUY",
    token: "BONK",
    amount: 1000000,
    price: 0.000012,
    timestamp: new Date(Date.now() - 1000 * 60 * 30),
    status: "COMPLETED",
  },
  {
    id: "2",
    botId: "pump-bot",
    type: "SELL",
    token: "PEPE",
    amount: 50000,
    price: 0.00089,
    pnl: 0.45,
    timestamp: new Date(Date.now() - 1000 * 60 * 45),
    status: "COMPLETED",
  },
  {
    id: "3",
    botId: "pump-bot",
    type: "BUY",
    token: "PEPE",
    amount: 50000,
    price: 0.00082,
    timestamp: new Date(Date.now() - 1000 * 60 * 60),
    status: "COMPLETED",
  },
  {
    id: "4",
    botId: "scalp-bot",
    type: "SELL",
    token: "SOL",
    amount: 0.1,
    price: 145.67,
    pnl: 0.12,
    timestamp: new Date(Date.now() - 1000 * 60 * 5),
    status: "COMPLETED",
  },
  // Additional trades for better archive data
  {
    id: "5",
    botId: "rsi-bot",
    type: "SELL",
    token: "BONK",
    amount: 1000000,
    price: 0.000015,
    pnl: 0.03,
    timestamp: new Date(Date.now() - 1000 * 60 * 10),
    status: "COMPLETED",
  },
  {
    id: "6",
    botId: "momentum-bot",
    type: "BUY",
    token: "WIF",
    amount: 100,
    price: 2.45,
    timestamp: new Date(Date.now() - 1000 * 60 * 120),
    status: "COMPLETED",
  },
  {
    id: "7",
    botId: "momentum-bot",
    type: "SELL",
    token: "WIF",
    amount: 100,
    price: 2.78,
    pnl: 0.33,
    timestamp: new Date(Date.now() - 1000 * 60 * 90),
    status: "COMPLETED",
  },
  {
    id: "8",
    botId: "scalp-bot",
    type: "BUY",
    token: "RAY",
    amount: 10,
    price: 4.23,
    timestamp: new Date(Date.now() - 1000 * 60 * 200),
    status: "COMPLETED",
  },
  {
    id: "9",
    botId: "scalp-bot",
    type: "SELL",
    token: "RAY",
    amount: 10,
    price: 3.98,
    pnl: -0.25,
    timestamp: new Date(Date.now() - 1000 * 60 * 180),
    status: "COMPLETED",
  },
];

// Function to get trades for a specific bot
export function getTradesForBot(botId: string): Trade[] {
  return mockTrades.filter((trade) => trade.botId === botId);
}

// Function to get bot by ID
export function getBotById(id: string): Bot | undefined {
  return mockBots.find((bot) => bot.id === id);
}

// Function to simulate real-time price updates
export function simulatePriceUpdate(currentPrice: number): number {
  const change = (Math.random() - 0.5) * 0.02; // ±1% change
  return Math.max(0, currentPrice * (1 + change));
}

// Function to generate random trade
export function generateRandomTrade(botId: string): Trade {
  const tokens = ["BONK", "PEPE", "WIF", "SOL", "DOGE"];
  const types: ("BUY" | "SELL")[] = ["BUY", "SELL"];

  return {
    id: Math.random().toString(36).substring(2, 11),
    botId,
    type: types[Math.floor(Math.random() * types.length)],
    token: tokens[Math.floor(Math.random() * tokens.length)],
    amount: Math.random() * 1000000,
    price: Math.random() * 10,
    pnl: Math.random() > 0.5 ? Math.random() * 2 : -Math.random() * 1,
    timestamp: new Date(),
    status: "COMPLETED",
  };
}
