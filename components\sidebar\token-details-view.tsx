"use client";

import { useState, useEffect } from "react";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import {
  TrendingUp,
  TrendingDown,
  Star,
  ExternalLink,
  Copy,
  Globe,
  Twitter,
  Github,
  MessageCircle,
  BarChart3,
  DollarSign,
  Users,
  Target,
  Zap,
  Activity,
  ArrowUpDown,
  Clock,
  Wallet,
  PieChart,
  TrendingUp as TrendingUpIcon,
} from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { CoinGeckoToken } from "@/lib/coingecko-service";

type Token = CoinGeckoToken;

interface TokenDetailsViewProps {
  isOpen: boolean;
  onClose: () => void;
  token: Token | null;
  defaultTab?: string;
}

export function TokenDetailsView({
  isOpen,
  onClose,
  token,
  defaultTab = "overview",
}: TokenDetailsViewProps) {
  const [activeTab, setActiveTab] = useState(defaultTab);

  // Reset tab when modal opens or token changes
  useEffect(() => {
    if (isOpen) {
      setActiveTab(defaultTab);
    }
  }, [isOpen, defaultTab]);

  if (!token) return null;

  const isPositive = (token.price_change_percentage_24h || 0) > 0;

  const formatPrice = (price: number | null | undefined) => {
    if (price === null || price === undefined || isNaN(price)) return "N/A";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 6,
    }).format(price);
  };

  const formatNumber = (num: number | null | undefined) => {
    if (num === null || num === undefined || isNaN(num)) return "N/A";
    if (num >= 1e12) return `${(num / 1e12).toFixed(2)}T`;
    if (num >= 1e9) return `${(num / 1e9).toFixed(2)}B`;
    if (num >= 1e6) return `${(num / 1e6).toFixed(2)}M`;
    if (num >= 1e3) return `${(num / 1e3).toFixed(2)}K`;
    return num.toLocaleString();
  };

  const copyTokenAddress = () => {
    const mockAddress = `${token.symbol.toUpperCase()}${Math.random()
      .toString(36)
      .substring(2, 15)}`;
    navigator.clipboard.writeText(mockAddress);
    toast.success(`${token.symbol.toUpperCase()} address copied`);
  };

  const mockLinks = {
    website: "https://example.com",
    twitter: "https://twitter.com/example",
    github: "https://github.com/example",
    telegram: "https://t.me/example",
    explorer: "https://explorer.solana.com/address/example",
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <img
              src={token.image}
              alt={token.name}
              className="w-8 h-8 rounded-full"
            />
            <div className="flex items-center gap-2">
              <span>{token.name}</span>
              <Badge variant="secondary">{token.symbol.toUpperCase()}</Badge>
              {token.market_cap_rank <= 10 && (
                <Star className="h-4 w-4 text-yellow-500 fill-current" />
              )}
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Price Header */}
          <div className="flex items-center justify-between">
            <div>
              <div className="text-3xl font-bold">
                {formatPrice(token.current_price)}
              </div>
              <div className="flex items-center gap-2 mt-1">
                {isPositive ? (
                  <TrendingUp className="h-4 w-4 text-green-500" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-500" />
                )}
                <span
                  className={cn(
                    "text-lg font-medium",
                    isPositive ? "text-green-500" : "text-red-500",
                  )}>
                  {isPositive ? "+" : ""}
                  {token.price_change_percentage_24h.toFixed(2)}%
                </span>
                <span className="text-muted-foreground">24h</span>
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={copyTokenAddress}>
                <Copy className="h-4 w-4 mr-2" />
                Copy Address
              </Button>
              <Button size="sm" className="bg-primary-gradient text-white">
                <Zap className="h-4 w-4 mr-2" />
                Start Trading
              </Button>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="portfolio">Portfolio</TabsTrigger>
              <TabsTrigger value="activity">Activity</TabsTrigger>
              <TabsTrigger value="markets">Markets</TabsTrigger>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="about">About</TabsTrigger>
            </TabsList>

            <TabsContent value="portfolio" className="space-y-6">
              {/* Portfolio Overview */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <Wallet className="h-4 w-4" />
                      Total Invested
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      ${formatNumber(Math.floor(Math.random() * 50000) + 10000)}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Across all purchases
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <PieChart className="h-4 w-4" />
                      Current Value
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      ${formatNumber(Math.floor(Math.random() * 60000) + 12000)}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Current holdings value
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <TrendingUpIcon className="h-4 w-4" />
                      Total P&L
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div
                      className={cn(
                        "text-2xl font-bold",
                        Math.random() > 0.5 ? "text-green-500" : "text-red-500",
                      )}>
                      {Math.random() > 0.5 ? "+" : "-"}$
                      {formatNumber(Math.floor(Math.random() * 15000) + 2000)}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {Math.random() > 0.5 ? "+" : "-"}
                      {(Math.random() * 30 + 5).toFixed(2)}% return
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <Target className="h-4 w-4" />
                      Holdings
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatNumber(
                        Math.floor(Math.random() * 10000000) + 1000000,
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {token.symbol.toUpperCase()} tokens
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Trading History */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    Your Trading History
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Mock user trading history */}
                    {[
                      {
                        type: "buy",
                        amount: "$5,250",
                        tokens: "525K",
                        time: "3 days ago",
                        price: "$0.000010",
                        pnl: "+$1,250",
                        pnlPercent: "+23.8%",
                        status: "profit",
                      },
                      {
                        type: "sell",
                        amount: "$3,180",
                        tokens: "300K",
                        time: "1 week ago",
                        price: "$0.000011",
                        pnl: "+$480",
                        pnlPercent: "+17.8%",
                        status: "profit",
                      },
                      {
                        type: "buy",
                        amount: "$8,900",
                        tokens: "950K",
                        time: "2 weeks ago",
                        price: "$0.000009",
                        pnl: "-$890",
                        pnlPercent: "-10.0%",
                        status: "loss",
                      },
                      {
                        type: "buy",
                        amount: "$12,500",
                        tokens: "1.2M",
                        time: "3 weeks ago",
                        price: "$0.000010",
                        pnl: "+$2,100",
                        pnlPercent: "+16.8%",
                        status: "profit",
                      },
                      {
                        type: "sell",
                        amount: "$6,750",
                        tokens: "750K",
                        time: "1 month ago",
                        price: "$0.000009",
                        pnl: "-$325",
                        pnlPercent: "-4.6%",
                        status: "loss",
                      },
                    ].map((trade, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-4 rounded-lg border">
                        <div className="flex items-center gap-3">
                          <div
                            className={cn(
                              "w-3 h-3 rounded-full",
                              trade.type === "buy"
                                ? "bg-blue-500"
                                : "bg-orange-500",
                            )}
                          />
                          <div>
                            <div className="font-medium">
                              <span
                                className={cn(
                                  "capitalize font-semibold",
                                  trade.type === "buy"
                                    ? "text-blue-500"
                                    : "text-orange-500",
                                )}>
                                {trade.type}
                              </span>{" "}
                              {trade.tokens} tokens
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {trade.time} • @ {trade.price}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">{trade.amount}</div>
                          <div
                            className={cn(
                              "text-sm font-medium",
                              trade.status === "profit"
                                ? "text-green-500"
                                : "text-red-500",
                            )}>
                            {trade.pnl} ({trade.pnlPercent})
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* View More Button */}
                  <div className="mt-4 text-center">
                    <Button variant="outline" className="w-full">
                      <Wallet className="h-4 w-4 mr-2" />
                      View Complete Trading History
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Portfolio Statistics */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart3 className="h-5 w-5" />
                      Trading Summary
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">
                          Total Trades
                        </span>
                        <span className="font-medium">
                          {Math.floor(Math.random() * 50) + 15}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">
                          Buy Orders
                        </span>
                        <span className="font-medium text-blue-500">
                          {Math.floor(Math.random() * 30) + 8}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">
                          Sell Orders
                        </span>
                        <span className="font-medium text-orange-500">
                          {Math.floor(Math.random() * 20) + 7}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">
                          Win Rate
                        </span>
                        <span className="font-medium text-green-500">
                          {(Math.random() * 30 + 60).toFixed(1)}%
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">
                          Avg Hold Time
                        </span>
                        <span className="font-medium">
                          {Math.floor(Math.random() * 10) + 2} days
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <PieChart className="h-5 w-5" />
                      Performance Metrics
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">
                          Best Trade
                        </span>
                        <span className="font-medium text-green-500">
                          +$
                          {formatNumber(
                            Math.floor(Math.random() * 5000) + 1000,
                          )}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">
                          Worst Trade
                        </span>
                        <span className="font-medium text-red-500">
                          -$
                          {formatNumber(Math.floor(Math.random() * 2000) + 500)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">
                          Avg Trade Size
                        </span>
                        <span className="font-medium">
                          $
                          {formatNumber(
                            Math.floor(Math.random() * 10000) + 2000,
                          )}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">
                          Total Fees Paid
                        </span>
                        <span className="font-medium">
                          ${formatNumber(Math.floor(Math.random() * 500) + 100)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">
                          ROI
                        </span>
                        <span
                          className={cn(
                            "font-medium",
                            Math.random() > 0.3
                              ? "text-green-500"
                              : "text-red-500",
                          )}>
                          {Math.random() > 0.3 ? "+" : "-"}
                          {(Math.random() * 40 + 10).toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="activity" className="space-y-6">
              {/* Trading Activity Overview */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <Activity className="h-4 w-4" />
                      Total Trades (24h)
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatNumber(Math.floor(Math.random() * 10000) + 1000)}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Buy & Sell transactions
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <ArrowUpDown className="h-4 w-4" />
                      Buy/Sell Ratio
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-500">
                      {(Math.random() * 2 + 0.5).toFixed(2)}:1
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Buys vs Sells
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Avg Trade Size
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      ${formatNumber(Math.floor(Math.random() * 50000) + 1000)}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Per transaction
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Recent Trading Activity */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    Recent Trading Activity
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Mock trading activity data */}
                    {[
                      {
                        type: "buy",
                        amount: "$12,450",
                        tokens: "1.2M",
                        time: "2 minutes ago",
                        price: "$0.000012",
                      },
                      {
                        type: "sell",
                        amount: "$8,320",
                        tokens: "850K",
                        time: "5 minutes ago",
                        price: "$0.000011",
                      },
                      {
                        type: "buy",
                        amount: "$25,600",
                        tokens: "2.1M",
                        time: "12 minutes ago",
                        price: "$0.000013",
                      },
                      {
                        type: "sell",
                        amount: "$5,890",
                        tokens: "520K",
                        time: "18 minutes ago",
                        price: "$0.000011",
                      },
                      {
                        type: "buy",
                        amount: "$18,750",
                        tokens: "1.5M",
                        time: "25 minutes ago",
                        price: "$0.000012",
                      },
                    ].map((trade, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 rounded-lg border">
                        <div className="flex items-center gap-3">
                          <div
                            className={cn(
                              "w-2 h-2 rounded-full",
                              trade.type === "buy"
                                ? "bg-green-500"
                                : "bg-red-500",
                            )}
                          />
                          <div>
                            <div className="font-medium">
                              <span
                                className={cn(
                                  "capitalize",
                                  trade.type === "buy"
                                    ? "text-green-500"
                                    : "text-red-500",
                                )}>
                                {trade.type}
                              </span>{" "}
                              {trade.tokens} tokens
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {trade.time}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">{trade.amount}</div>
                          <div className="text-sm text-muted-foreground">
                            @ {trade.price}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* View More Button */}
                  <div className="mt-4 text-center">
                    <Button variant="outline" className="w-full">
                      <Activity className="h-4 w-4 mr-2" />
                      View All Trading Activity
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Trading Statistics */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Trading Statistics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-3 rounded-lg border">
                      <div className="text-2xl font-bold text-green-500">
                        {Math.floor(Math.random() * 5000) + 2000}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Buy Orders (24h)
                      </div>
                    </div>
                    <div className="text-center p-3 rounded-lg border">
                      <div className="text-2xl font-bold text-red-500">
                        {Math.floor(Math.random() * 3000) + 1500}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Sell Orders (24h)
                      </div>
                    </div>
                    <div className="text-center p-3 rounded-lg border">
                      <div className="text-2xl font-bold">
                        {Math.floor(Math.random() * 1000) + 500}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Unique Traders
                      </div>
                    </div>
                    <div className="text-center p-3 rounded-lg border">
                      <div className="text-2xl font-bold">
                        $
                        {formatNumber(
                          Math.floor(Math.random() * 1000000) + 500000,
                        )}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Total Volume (24h)
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="markets" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Market Data</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">
                        Current Price
                      </span>
                      <span className="font-medium">
                        {formatPrice(token.current_price)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">24h Change</span>
                      <span
                        className={cn(
                          "font-medium",
                          isPositive ? "text-green-500" : "text-red-500",
                        )}>
                        {isPositive ? "+" : ""}
                        {token.price_change_percentage_24h.toFixed(2)}%
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">24h Volume</span>
                      <span className="font-medium">
                        ${formatNumber(token.total_volume)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">Market Cap</span>
                      <span className="font-medium">
                        ${formatNumber(token.market_cap)}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="overview" className="space-y-6">
              {/* Key Metrics */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <BarChart3 className="h-4 w-4" />
                      Market Cap
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      ${formatNumber(token.market_cap)}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Rank #{token.market_cap_rank}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      Volume (24h)
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      ${formatNumber(token.total_volume)}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Trading volume
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      Circulating Supply
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatNumber(token.circulating_supply)}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {token.symbol.toUpperCase()}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <Target className="h-4 w-4" />
                      Max Supply
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {token.max_supply ? formatNumber(token.max_supply) : "∞"}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {token.symbol.toUpperCase()}
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Price Statistics */}
              <Card>
                <CardHeader>
                  <CardTitle>Price Statistics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">
                        All-Time High
                      </p>
                      <p className="text-lg font-semibold">
                        {formatPrice(token.ath)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">
                        All-Time Low
                      </p>
                      <p className="text-lg font-semibold">
                        {formatPrice(token.atl)}
                      </p>
                    </div>
                  </div>
                  <Separator />
                  <div>
                    <p className="text-sm text-muted-foreground">
                      Last Updated
                    </p>
                    <p className="text-sm">
                      {new Date(token.last_updated).toLocaleString()}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="about" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>About {token.name}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-muted-foreground">
                    {token.name} ({token.symbol.toUpperCase()}) is a
                    cryptocurrency token. This is a mock description for
                    demonstration purposes. In a real application, this would
                    contain detailed information about the token&apos;s purpose,
                    technology, and use cases.
                  </p>

                  <div>
                    <h4 className="font-medium mb-2">Links</h4>
                    <div className="flex flex-wrap gap-2">
                      <Button variant="outline" size="sm" asChild>
                        <a
                          href={mockLinks.website}
                          target="_blank"
                          rel="noopener noreferrer">
                          <Globe className="h-4 w-4 mr-2" />
                          Website
                        </a>
                      </Button>
                      <Button variant="outline" size="sm" asChild>
                        <a
                          href={mockLinks.twitter}
                          target="_blank"
                          rel="noopener noreferrer">
                          <Twitter className="h-4 w-4 mr-2" />
                          Twitter
                        </a>
                      </Button>
                      <Button variant="outline" size="sm" asChild>
                        <a
                          href={mockLinks.github}
                          target="_blank"
                          rel="noopener noreferrer">
                          <Github className="h-4 w-4 mr-2" />
                          GitHub
                        </a>
                      </Button>
                      <Button variant="outline" size="sm" asChild>
                        <a
                          href={mockLinks.telegram}
                          target="_blank"
                          rel="noopener noreferrer">
                          <MessageCircle className="h-4 w-4 mr-2" />
                          Telegram
                        </a>
                      </Button>
                      <Button variant="outline" size="sm" asChild>
                        <a
                          href={mockLinks.explorer}
                          target="_blank"
                          rel="noopener noreferrer">
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Explorer
                        </a>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
}
