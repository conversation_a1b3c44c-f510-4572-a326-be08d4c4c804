"use client";

import { ReactNode, createContext, useContext } from "react";
import { supabase } from "@/lib/supabase";
import { SupabaseClient } from "@supabase/supabase-js";

// Create Supabase context
const SupabaseContext = createContext<SupabaseClient | null>(null);

export function useSupabase() {
  const context = useContext(SupabaseContext);
  if (!context) {
    throw new Error("useSupabase must be used within a SupabaseProvider");
  }
  return context;
}

export default function SupabaseProvider({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <SupabaseContext.Provider value={supabase}>
      {children}
    </SupabaseContext.Provider>
  );
}
