"use client";

import { useState } from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { ThemeSwitcher } from "@/components/theme-switcher";
import Image from "next/image";
import { SignedIn, SignedOut, SignOutButton } from "@clerk/nextjs";
import EmailVerificationModal from "./EmailVerificationModal";

export function Navigation() {
  const [isEmailVerificationOpen, setIsEmailVerificationOpen] = useState(false);

  return (
    <nav className=" backdrop-blur-sm sticky top-0 z-50">
      <div className="container mx-auto px-6">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center space-x-2"
            >
              <Image
                src="/logo.png"
                alt="DexTrip Logo"
                width={30}
                height={30}
              />
              <span className="text-lg sm:text-xl font-bold text-primary">
                DEXTRIP
              </span>
            </motion.div>
          </Link>

          {/* Desktop Navigation */}
          <div className="flex items-center space-x-6">
            {/* <NetworkStatus /> */}
            <ThemeSwitcher />
            <SignedOut>
              <div className="flex items-center gap-4">
                <Button
                  onClick={() => setIsEmailVerificationOpen(true)}
                  className="bg-primary-gradient hover:opacity-90 text-white border-0 hover:shadow-xl transition-all duration-300"
                >
                  Join Waitlist
                </Button>
                <EmailVerificationModal
                  isOpen={isEmailVerificationOpen}
                  onClose={() => setIsEmailVerificationOpen(false)}
                />
              </div>
            </SignedOut>
            <SignedIn>
              <SignOutButton>
                <Button className="bg-primary-gradient hover:opacity-90 text-white border-0 hover:shadow-xl transition-all duration-300">
                  Exit DexTrip
                </Button>
              </SignOutButton>
            </SignedIn>
          </div>
        </div>
      </div>
    </nav>
  );
}
