"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>b,
  Bread<PERSON>rumbItem,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbLink,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {

  CreditCard,
  ArrowUpRight,
  ArrowDownLeft,
  Plus,
  Eye,
  EyeOff,
  Copy,
  ExternalLink,
  TrendingUp,
  TrendingDown,
  Activity,
  History,
  ChevronDown,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { PortfolioSummary } from "@/components/portfolio-summary";

export default function WalletPage() {
  const [hideBalance, setHideBalance] = useState(false);
  const [selectedWallet, setSelectedWallet] = useState(0);

  const formatAddress = (address: string) => {
    if (!address) return "";
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const walletCards = [
    {
      id: 1,
      name: "Main Wallet",
      type: "Hot Wallet",
      balance: 5432.18,
      change: 2.34,
      address: "7xKXvR9mNpQs3kLm8vQrTyHn2Jd4Fg5Bh6Aw1Zx9mNp",
      isMain: true,
    },
    {
      id: 2,
      name: "Trading Wallet",
      type: "Trading",
      balance: 1250.75,
      change: -1.2,
      address: "*******************************************",
      isMain: false,
    },
    {
      id: 3,
      name: "Savings Wallet",
      type: "Cold Storage",
      balance: 8900.5,
      change: 0.85,
      address: "9mNp7xKXvR9mNpQs3kLm8vQrTyHn2Jd4Fg5Bh6Aw1Zx",
      isMain: false,
    },
  ];

  const transactions = [
    {
      id: 1,
      type: "send",
      amount: 0.5,
      token: "SOL",
      to: "7xKX...9mNp",
      timestamp: "2 hours ago",
      status: "completed",
      hash: "5KJp9mNp7xKXvR9mNpQs3kLm8vQrTyHn2Jd4Fg5Bh6Aw1Zx",
    },
    {
      id: 2,
      type: "receive",
      amount: 1000,
      token: "USDC",
      from: "3kLm...8vQr",
      timestamp: "5 hours ago",
      status: "completed",
      hash: "8vQrTyHn2Jd4Fg5Bh6Aw1Zx9mNp7xKXvR9mNpQs3kLm",
    },
    {
      id: 3,
      type: "swap",
      amount: 2.3,
      token: "SOL",
      to: "1500 USDC",
      timestamp: "1 day ago",
      status: "completed",
      hash: "2Jd4Fg5Bh6Aw1Zx9mNp7xKXvR9mNpQs3kLm8vQrTyHn",
    },
    {
      id: 4,
      type: "send",
      amount: 250,
      token: "USDC",
      to: "9mNp...1Zx",
      timestamp: "2 days ago",
      status: "completed",
      hash: "Fg5Bh6Aw1Zx9mNp7xKXvR9mNpQs3kLm8vQrTyHn2Jd4",
    },
    {
      id: 5,
      type: "receive",
      amount: 0.8,
      token: "SOL",
      from: "5Bh6...Aw1",
      timestamp: "3 days ago",
      status: "completed",
      hash: "6Aw1Zx9mNp7xKXvR9mNpQs3kLm8vQrTyHn2Jd4Fg5Bh",
    },
  ];

  return (
    <>
      <header className="flex h-16 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className="hidden md:block" />
              <BreadcrumbItem>
                <BreadcrumbPage>Wallet</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
        <div className="mr-3">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center">
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  {walletCards[selectedWallet].name}
                  <Badge variant="default" className="text-xs">
                    {walletCards[selectedWallet].isMain ? "Main" : "Active"}
                  </Badge>
                </CardTitle>
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              {walletCards.map((wallet, index) => (
                <DropdownMenuItem
                  key={wallet.id}
                  onClick={() => setSelectedWallet(index)}
                >
                  <CreditCard className="h-4 w-4 mr-2" />
                  {wallet.name}
                  {wallet.isMain && (
                    <Badge variant="outline" className="ml-2 text-xs">
                      Main
                    </Badge>
                  )}
                </DropdownMenuItem>
              ))}
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Plus className="h-4 w-4 mr-2" />
                Create New Wallet
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </header>
      <div className="h-px bg-border" />

      <div className="flex flex-1 flex-col gap-4 p-4">
        <div className="min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min">
          {/* Main Content - Two Column Layout */}
          {/* <div className="grid grid-cols-1 lg:grid-cols-2 gap-6"> */}
          {/* Left Column - Wallet Card and Actions */}
          <div className="space-y-6">
            {/* Single Wallet Card with Dropdown */}
            <Card className="ring-2 ring-primary/20">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setHideBalance(!hideBalance)}
                    >
                      {hideBalance ? (
                        <Eye className="h-4 w-4" />
                      ) : (
                        <EyeOff className="h-4 w-4" />
                      )}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        copyToClipboard(walletCards[selectedWallet].address)
                      }
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">
                  {walletCards[selectedWallet].type}
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <p className="text-sm text-muted-foreground">Address</p>
                    <p className="font-mono text-sm">
                      {formatAddress(walletCards[selectedWallet].address)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">
                      Total Balance
                    </p>
                    <div className="flex items-center gap-2">
                      <p className="text-4xl font-bold">
                        {hideBalance
                          ? "••••••"
                          : `$${walletCards[selectedWallet].balance.toFixed(2)}`}
                      </p>
                      <div
                        className={`flex items-center text-sm ${
                          walletCards[selectedWallet].change >= 0
                            ? "text-green-600"
                            : "text-red-600"
                        }`}
                      >
                        {walletCards[selectedWallet].change >= 0 ? (
                          <TrendingUp className="h-4 w-4 mr-1" />
                        ) : (
                          <TrendingDown className="h-4 w-4 mr-1" />
                        )}
                        {walletCards[selectedWallet].change >= 0 ? "+" : ""}
                        {walletCards[selectedWallet].change}%
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Swap Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-4 gap-4">
                  <Button variant="outline" className="h-16 flex-col gap-2">
                    <ArrowUpRight className="h-6 w-6" />
                    Send
                  </Button>
                  <Button variant="outline" className="h-16 flex-col gap-2">
                    <ArrowDownLeft className="h-6 w-6" />
                    Receive
                  </Button>
                  <Button variant="outline" className="h-16 flex-col gap-2">
                    <Activity className="h-6 w-6" />
                    Swap
                  </Button>
                  <Button variant="outline" className="h-16 flex-col gap-2">
                    <Plus className="h-6 w-6" />
                    Buy Crypto
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Portfolio Summary */}
            <PortfolioSummary limit={5} showHeader={true} />
          </div>

          {/* Right Column - Transactions */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <History className="h-5 w-5" />
                    All Transactions
                  </CardTitle>
                  <Button variant="outline" size="sm">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {transactions.map((tx) => (
                    <div
                      key={tx.id}
                      className="flex items-center gap-4 p-4 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                    >
                      <div
                        className={`p-2 rounded-full ${
                          tx.type === "send"
                            ? "bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400"
                            : tx.type === "receive"
                              ? "bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400"
                              : "bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400"
                        }`}
                      >
                        {tx.type === "send" ? (
                          <ArrowUpRight className="h-4 w-4" />
                        ) : tx.type === "receive" ? (
                          <ArrowDownLeft className="h-4 w-4" />
                        ) : (
                          <Activity className="h-4 w-4" />
                        )}
                      </div>

                      <div className="flex-1">
                        <div className="font-medium capitalize">
                          {tx.type} {tx.token}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {tx.type === "send"
                            ? `To ${tx.to}`
                            : tx.type === "receive"
                              ? `From ${tx.from}`
                              : `Swapped to ${tx.to}`}
                        </div>
                        <div className="text-xs text-muted-foreground font-mono">
                          {formatAddress(tx.hash)}
                        </div>
                      </div>

                      <div className="text-right">
                        <div
                          className={`font-medium ${
                            tx.type === "send"
                              ? "text-red-600"
                              : tx.type === "receive"
                                ? "text-green-600"
                                : "text-blue-600"
                          }`}
                        >
                          {tx.type === "send"
                            ? "-"
                            : tx.type === "receive"
                              ? "+"
                              : ""}
                          {tx.amount} {tx.token}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {tx.timestamp}
                        </div>
                      </div>

                      <div className="flex flex-col gap-2">
                        <Badge
                          variant={
                            tx.status === "completed" ? "default" : "secondary"
                          }
                        >
                          {tx.status}
                        </Badge>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(tx.hash)}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
          {/* </div> */}
        </div>
      </div>
    </>
  );
}
