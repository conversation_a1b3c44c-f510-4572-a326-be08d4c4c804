"use client";

import { useState, useMemo } from "react";
import {
  Trash2,
  Search,
  Filter,
  SortAsc,
  SortDesc,
  TrendingUp,
  Activity,
  BarChart3,
  DollarSign,
} from "lucide-react";
import { BotCard } from "@/components/bot-card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useBots } from "@/contexts/bot-context";
import { toast } from "sonner";
import { SidebarTrigger } from "@/components/ui/sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";
import { Separator } from "@/components/ui/separator";
import { motion } from "framer-motion";

type SortOption = "name" | "pnl" | "winRate" | "totalTrades" | "created";
type FilterOption = "all" | "active" | "inactive" | "profitable" | "losing";

export default function Home() {
  const { bots, customBots, startBot, stopBot, deleteCustomBot } = useBots();

  // State for filtering and sorting
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<SortOption>("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [filterBy, setFilterBy] = useState<FilterOption>("all");

  // Separate default and custom bots
  const defaultBots = bots.filter((bot) => !bot.id.startsWith("custom-"));

  const handleDeleteCustomBot = (botId: string, botName: string) => {
    deleteCustomBot(botId);
    toast.success(`${botName} deleted successfully`);
  };

  // Filter and sort bots
  const filteredAndSortedBots = useMemo(() => {
    let allBots = [...defaultBots, ...customBots];

    // Apply search filter
    if (searchQuery) {
      allBots = allBots.filter(
        (bot) =>
          bot.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          bot.description.toLowerCase().includes(searchQuery.toLowerCase()),
      );
    }

    // Apply status filter
    switch (filterBy) {
      case "active":
        allBots = allBots.filter((bot) => bot.isActive);
        break;
      case "inactive":
        allBots = allBots.filter((bot) => !bot.isActive);
        break;
      case "profitable":
        allBots = allBots.filter((bot) => bot.stats.pnl > 0);
        break;
      case "losing":
        allBots = allBots.filter((bot) => bot.stats.pnl < 0);
        break;
    }

    // Apply sorting
    allBots.sort((a, b) => {
      let aValue: string | number, bValue: string | number;

      switch (sortBy) {
        case "name":
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case "pnl":
          aValue = a.stats.pnl;
          bValue = b.stats.pnl;
          break;
        case "winRate":
          aValue = a.stats.winRate;
          bValue = b.stats.winRate;
          break;
        case "totalTrades":
          aValue = a.stats.totalTrades;
          bValue = b.stats.totalTrades;
          break;
        case "created":
          aValue = a.id.startsWith("custom-") ? 1 : 0;
          bValue = b.id.startsWith("custom-") ? 1 : 0;
          break;
        default:
          return 0;
      }

      if (sortOrder === "asc") {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return allBots;
  }, [defaultBots, customBots, searchQuery, filterBy, sortBy, sortOrder]);

  // Calculate summary stats
  const summaryStats = useMemo(() => {
    const allBots = [...defaultBots, ...customBots];
    return {
      total: allBots.length,
      active: allBots.filter((bot) => bot.isActive).length,
      profitable: allBots.filter((bot) => bot.stats.pnl > 0).length,
      totalPnL: allBots.reduce((sum, bot) => sum + bot.stats.pnl, 0),
    };
  }, [defaultBots, customBots]);

  return (
    <div>
      <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 justify-between pr-6">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbPage>BOT DASHBOARD</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
        <Link href="/create-bot">
          <Button className="bg-primary-gradient text-white">
            Create New Bot
          </Button>
        </Link>
      </header>
      <div className="h-px bg-border" />

      {/* Summary Stats */}
      <div className="px-6 pt-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"
        >
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-500/10 rounded-lg">
                  <BarChart3 className="h-5 w-5 text-blue-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total Bots</p>
                  <p className="text-2xl font-bold">{summaryStats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-500/10 rounded-lg">
                  <Activity className="h-5 w-5 text-green-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Active Bots</p>
                  <p className="text-2xl font-bold">{summaryStats.active}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-500/10 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-purple-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Profitable</p>
                  <p className="text-2xl font-bold">
                    {summaryStats.profitable}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div
                  className={`p-2 rounded-lg ${
                    summaryStats.totalPnL >= 0
                      ? "bg-green-500/10"
                      : "bg-red-500/10"
                  }`}
                >
                  <DollarSign
                    className={`h-5 w-5 ${
                      summaryStats.totalPnL >= 0
                        ? "text-green-500"
                        : "text-red-500"
                    }`}
                  />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total P&L</p>
                  <p
                    className={`text-2xl font-bold ${
                      summaryStats.totalPnL >= 0
                        ? "text-green-500"
                        : "text-red-500"
                    }`}
                  >
                    {summaryStats.totalPnL >= 0 ? "+" : ""}
                    {summaryStats.totalPnL.toFixed(4)} SOL
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="flex flex-col sm:flex-row gap-4"
        >
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search bots..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <Select
            value={filterBy}
            onValueChange={(value: FilterOption) => setFilterBy(value)}
          >
            <SelectTrigger className="w-full sm:w-[180px]">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Bots</SelectItem>
              <SelectItem value="active">Active Only</SelectItem>
              <SelectItem value="inactive">Inactive Only</SelectItem>
              <SelectItem value="profitable">Profitable</SelectItem>
              <SelectItem value="losing">Losing</SelectItem>
            </SelectContent>
          </Select>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="w-full sm:w-auto">
                {sortOrder === "asc" ? (
                  <SortAsc className="h-4 w-4 mr-2" />
                ) : (
                  <SortDesc className="h-4 w-4 mr-2" />
                )}
                Sort by {sortBy}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setSortBy("name")}>
                Name
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy("pnl")}>
                P&L
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy("winRate")}>
                Win Rate
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy("totalTrades")}>
                Total Trades
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy("created")}>
                Type
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  setSortOrder(sortOrder === "asc" ? "desc" : "asc")
                }
              >
                {sortOrder === "asc" ? "Descending" : "Ascending"}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </motion.div>
      </div>

      {/* Bot Monitoring */}
      {/* {summaryStats.active > 0 && (
        <div className="px-6 pb-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <BotMonitoring />
          </motion.div>
        </div>
      )} */}

      {/* Bot Grid */}
      <div className="p-6">
        {filteredAndSortedBots.length > 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
          >
            {filteredAndSortedBots.map((bot, index) => (
              <motion.div
                key={bot.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 * index }}
                className="relative group"
              >
                <BotCard bot={bot} onStartBot={startBot} onStopBot={stopBot} />
                {bot.id.startsWith("custom-") && (
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => handleDeleteCustomBot(bot.id, bot.name)}
                    disabled={bot.isActive}
                    className="absolute top-2 right-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity z-10"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                )}
                {/* Bot Type Badge */}
                <Badge
                  variant={
                    bot.id.startsWith("custom-") ? "secondary" : "default"
                  }
                  className="absolute top-2 left-2 text-xs"
                >
                  {bot.id.startsWith("custom-") ? "Custom" : "Default"}
                </Badge>
              </motion.div>
            ))}
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-12 border-2 border-dashed border-border rounded-lg"
          >
            <div className="flex flex-col items-center gap-4">
              <div className="p-4 bg-muted/50 rounded-full">
                <Activity className="h-8 w-8 text-muted-foreground" />
              </div>
              <div>
                <h3 className="text-lg font-medium mb-2">No bots found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchQuery || filterBy !== "all"
                    ? "Try adjusting your search or filter criteria."
                    : "Create your first bot to get started with automated trading."}
                </p>
                {!searchQuery && filterBy === "all" && (
                  <Link href="/create-bot">
                    <Button className="bg-primary-gradient text-white">
                      Create Your First Bot
                    </Button>
                  </Link>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}
