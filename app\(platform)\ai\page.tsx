"use client";

import React, { useState } from "react";

import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Star,
  Bot,
  X,
  Check,
  RotateCcw,
  Zap,
  Target,
  Shield,
  Brain,
  ChevronDown,
  Filter,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";

import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { motion, useMotionValue, useTransform, PanInfo } from "framer-motion";

interface BotCard {
  id: string;
  name: string;
  description: string;
  strategy: string;
  performance: {
    roi: number;
    winRate: number;
    totalTrades: number;
    avgProfit: number;
  };
  riskLevel: "Low" | "Medium" | "High";
  minInvestment: number;
  tags: string[];
  icon: React.ReactNode;
  gradient: string;
}

export default function AIPage() {
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [swipedCards, setSwipedCards] = useState<{
    [key: string]: "start" | "cancel";
  }>({});
  const [selectedBotType, setSelectedBotType] = useState("All Bots");
  const [filterOpen, setFilterOpen] = useState(false);
  const [filters, setFilters] = useState({
    minROI: [0],
    maxRisk: "High",
    minInvestment: [0],
    autoRebalance: true,
    stopLoss: true,
  });

  const botCards: BotCard[] = [
    {
      id: "1",
      name: "DeFi Yield Hunter",
      description:
        "Automatically finds and invests in high-yield DeFi opportunities across multiple protocols.",
      strategy: "Yield Farming & Liquidity Mining",
      performance: {
        roi: 24.5,
        winRate: 78,
        totalTrades: 156,
        avgProfit: 12.3,
      },
      riskLevel: "Medium",
      minInvestment: 1000,
      tags: ["DeFi", "Yield", "Auto-compound"],
      icon: <Target className="h-6 w-6" />,
      gradient: "from-blue-500 to-purple-600",
    },
    {
      id: "2",
      name: "Arbitrage Master",
      description:
        "Exploits price differences across exchanges for risk-free profits.",
      strategy: "Cross-Exchange Arbitrage",
      performance: {
        roi: 18.2,
        winRate: 92,
        totalTrades: 342,
        avgProfit: 8.7,
      },
      riskLevel: "Low",
      minInvestment: 500,
      tags: ["Arbitrage", "Low Risk", "High Frequency"],
      icon: <Zap className="h-6 w-6" />,
      gradient: "from-green-500 to-teal-600",
    },
    {
      id: "3",
      name: "Trend Follower Pro",
      description:
        "Uses advanced AI to identify and ride market trends for maximum profits.",
      strategy: "Technical Analysis & Trend Following",
      performance: {
        roi: 31.8,
        winRate: 65,
        totalTrades: 89,
        avgProfit: 28.4,
      },
      riskLevel: "High",
      minInvestment: 2000,
      tags: ["AI", "Trends", "Technical Analysis"],
      icon: <Brain className="h-6 w-6" />,
      gradient: "from-orange-500 to-red-600",
    },
    {
      id: "4",
      name: "Safe Haven",
      description:
        "Conservative strategy focused on capital preservation with steady returns.",
      strategy: "Conservative Portfolio Management",
      performance: {
        roi: 12.4,
        winRate: 89,
        totalTrades: 67,
        avgProfit: 15.2,
      },
      riskLevel: "Low",
      minInvestment: 250,
      tags: ["Conservative", "Stable", "Long-term"],
      icon: <Shield className="h-6 w-6" />,
      gradient: "from-gray-500 to-slate-600",
    },
    {
      id: "5",
      name: "Meme Coin Hunter",
      description:
        "Identifies trending meme coins early for explosive growth potential.",
      strategy: "Social Sentiment & Momentum Trading",
      performance: {
        roi: 45.7,
        winRate: 52,
        totalTrades: 234,
        avgProfit: 67.3,
      },
      riskLevel: "High",
      minInvestment: 100,
      tags: ["Meme", "High Risk", "Social Trading"],
      icon: <Star className="h-6 w-6" />,
      gradient: "from-pink-500 to-violet-600",
    },
  ];

  const currentCard = botCards[currentCardIndex];
  const x = useMotionValue(0);
  const rotate = useTransform(x, [-200, 200], [-25, 25]);
  const opacity = useTransform(x, [-200, -100, 0, 100, 200], [0, 1, 1, 1, 0]);

  // Swipe visual feedback
  const leftGradientOpacity = useTransform(x, [-150, -50, 0], [1, 0.5, 0]);
  const rightGradientOpacity = useTransform(x, [0, 50, 150], [0, 0.5, 1]);
  const centerIconOpacity = useTransform(
    x,
    [-100, -50, 0, 50, 100],
    [1, 0.5, 0, 0.5, 1],
  );
  const centerIconScale = useTransform(
    x,
    [-100, -50, 0, 50, 100],
    [1.2, 1, 0.8, 1, 1.2],
  );

  const botTypes = [
    "All Bots",
    "DeFi",
    "Arbitrage",
    "Trend Following",
    "Conservative",
    "Meme Coins",
  ];

  const handleDragEnd = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    const threshold = 100;

    if (info.offset.x > threshold) {
      // Swiped right - Start bot
      handleStartBot();
    } else if (info.offset.x < -threshold) {
      // Swiped left - Cancel/Skip
      handleCancelBot();
    } else {
      // Snap back to center
      x.set(0);
    }
  };

  const handleStartBot = () => {
    if (currentCard) {
      setSwipedCards((prev) => ({ ...prev, [currentCard.id]: "start" }));
      nextCard();
    }
  };

  const handleCancelBot = () => {
    if (currentCard) {
      setSwipedCards((prev) => ({ ...prev, [currentCard.id]: "cancel" }));
      nextCard();
    }
  };

  const nextCard = () => {
    x.set(0);
    if (currentCardIndex < botCards.length - 1) {
      setCurrentCardIndex((prev) => prev + 1);
    }
  };

  const resetCards = () => {
    setCurrentCardIndex(0);
    setSwipedCards({});
    x.set(0);
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case "Low":
        return "text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-400";
      case "Medium":
        return "text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-400";
      case "High":
        return "text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-400";
      default:
        return "text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-400";
    }
  };

  return (
    <>
      <header className="flex h-16 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          {/* Bot Type Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <Bot className="h-4 w-4" />
                {selectedBotType}
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              {botTypes.map((type) => (
                <DropdownMenuItem
                  key={type}
                  onClick={() => setSelectedBotType(type)}
                >
                  {type}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <div className="flex item-center gap-2 pr-4">
          {/* Configure Dialog */}
          <Dialog open={filterOpen} onOpenChange={setFilterOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Configure
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>Configure Bot</DialogTitle>
              </DialogHeader>
              <div className="space-y-6 py-4">
                <div className="space-y-2">
                  <Label>Minimum ROI (%)</Label>
                  <Slider
                    value={filters.minROI}
                    onValueChange={(value) =>
                      setFilters((prev) => ({ ...prev, minROI: value }))
                    }
                    max={50}
                    step={1}
                    className="w-full"
                  />
                  <div className="text-sm text-muted-foreground">
                    {filters.minROI[0]}% or higher
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Maximum Investment ($)</Label>
                  <Slider
                    value={filters.minInvestment}
                    onValueChange={(value) =>
                      setFilters((prev) => ({
                        ...prev,
                        minInvestment: value,
                      }))
                    }
                    max={5000}
                    step={100}
                    className="w-full"
                  />
                  <div className="text-sm text-muted-foreground">
                    Up to ${filters.minInvestment[0]}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="auto-rebalance">Auto Rebalancing</Label>
                  <Switch
                    id="auto-rebalance"
                    checked={filters.autoRebalance}
                    onCheckedChange={(checked) =>
                      setFilters((prev) => ({
                        ...prev,
                        autoRebalance: checked,
                      }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="stop-loss">Stop Loss Protection</Label>
                  <Switch
                    id="stop-loss"
                    checked={filters.stopLoss}
                    onCheckedChange={(checked) =>
                      setFilters((prev) => ({
                        ...prev,
                        stopLoss: checked,
                      }))
                    }
                  />
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </header>
      <div className="h-px bg-border" />

      <div className="flex flex-1 flex-col gap-4 p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4"></div>

          <div className="flex items-center gap-2">
            {/* <Button onClick={resetCards} variant="outline">
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button> */}
          </div>
        </div>

        {currentCardIndex < botCards.length ? (
          <div className="flex justify-center">
            <div className="relative w-full max-w-md">
              {/* Card Stack Background */}
              {botCards
                .slice(currentCardIndex + 1, currentCardIndex + 3)
                .map((card, index) => (
                  <div
                    key={card.id}
                    className="absolute inset-0 bg-card border rounded-2xl shadow-lg"
                    style={{
                      transform: `scale(${1 - (index + 1) * 0.05}) translateY(${(index + 1) * 8}px)`,
                      zIndex: -index - 1,
                      opacity: 1 - (index + 1) * 0.3,
                    }}
                  />
                ))}

              {/* Main Card */}
              <motion.div
                drag="x"
                dragConstraints={{ left: 0, right: 0 }}
                style={{ x, rotate, opacity }}
                onDragEnd={handleDragEnd}
                className="relative cursor-grab active:cursor-grabbing"
                whileDrag={{ scale: 1.05 }}
              >
                {/* Swipe Visual Feedback Overlays */}
                <motion.div
                  style={{ opacity: leftGradientOpacity }}
                  className="absolute inset-0 bg-gradient-to-r from-red-500/80 to-red-600/80 rounded-2xl z-10 pointer-events-none"
                />
                <motion.div
                  style={{ opacity: rightGradientOpacity }}
                  className="absolute inset-0 bg-gradient-to-r from-purple-500/80 to-purple-600/80 rounded-2xl z-10 pointer-events-none"
                />

                {/* Center Icon */}
                <motion.div
                  style={{
                    opacity: centerIconOpacity,
                    scale: centerIconScale,
                  }}
                  className="absolute inset-0 flex items-center justify-center z-20 pointer-events-none"
                >
                  <motion.div className="bg-white/90 dark:bg-black/90 rounded-full p-6 shadow-2xl">
                    {x.get() < -50 ? (
                      <X className="h-12 w-12 text-red-500" />
                    ) : x.get() > 50 ? (
                      <Check className="h-12 w-12 text-purple-500" />
                    ) : null}
                  </motion.div>
                </motion.div>

                <Card className="w-full h-[600px] shadow-2xl border-2 relative z-0">
                  <div
                    className={`h-32 bg-gradient-to-r ${currentCard.gradient} rounded-t-lg relative overflow-hidden`}
                  >
                    <div className="absolute inset-0 bg-black/20" />
                    <div className="absolute top-4 left-4 text-white">
                      {currentCard.icon}
                    </div>
                    <div className="absolute bottom-4 left-4 text-white">
                      <h2 className="text-2xl font-bold">{currentCard.name}</h2>
                      <p className="text-white/80">{currentCard.strategy}</p>
                    </div>
                  </div>

                  <CardContent className="p-6 space-y-6">
                    <p className="text-muted-foreground leading-relaxed">
                      {currentCard.description}
                    </p>

                    {/* Performance Metrics */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 bg-muted/50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">
                          +{currentCard.performance.roi}%
                        </div>
                        <div className="text-sm text-muted-foreground">ROI</div>
                      </div>
                      <div className="text-center p-3 bg-muted/50 rounded-lg">
                        <div className="text-2xl font-bold">
                          {currentCard.performance.winRate}%
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Win Rate
                        </div>
                      </div>
                      <div className="text-center p-3 bg-muted/50 rounded-lg">
                        <div className="text-2xl font-bold">
                          {currentCard.performance.totalTrades}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Trades
                        </div>
                      </div>
                      <div className="text-center p-3 bg-muted/50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">
                          ${currentCard.performance.avgProfit}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Avg Profit
                        </div>
                      </div>
                    </div>

                    {/* Risk & Investment */}
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Risk Level</span>
                        <Badge className={getRiskColor(currentCard.riskLevel)}>
                          {currentCard.riskLevel}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">
                          Min Investment
                        </span>
                        <span className="font-bold">
                          ${currentCard.minInvestment}
                        </span>
                      </div>
                    </div>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-2">
                      {currentCard.tags.map((tag) => (
                        <Badge
                          key={tag}
                          variant="secondary"
                          className="text-xs"
                        >
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Side Action Buttons for Desktop */}
              <div className="hidden md:flex absolute inset-y-0 -left-32 -right-32 items-center justify-between pointer-events-none">
                <Button
                  onClick={handleCancelBot}
                  variant="outline"
                  size="lg"
                  className="w-20 h-20 rounded-full border-2 border-red-200 hover:border-red-400 hover:bg-red-50 dark:hover:bg-red-950 pointer-events-auto"
                >
                  <X className="h-8 w-8 text-red-500" />
                </Button>
                <Button
                  onClick={handleStartBot}
                  size="lg"
                  className="w-20 h-20 rounded-full bg-purple-500 hover:bg-purple-600 border-2 border-purple-400 pointer-events-auto"
                >
                  <Check className="h-8 w-8 text-white" />
                </Button>
              </div>

              {/* Action Buttons for Mobile */}
              <div className="flex justify-between mt-6 md:hidden">
                <Button
                  onClick={handleCancelBot}
                  variant="outline"
                  size="lg"
                  className="w-24 h-24 rounded-full border-2 border-red-200 hover:border-red-400 hover:bg-red-50 dark:hover:bg-red-950"
                >
                  <X className="h-8 w-8 text-red-500" />
                </Button>
                <Button
                  onClick={handleStartBot}
                  size="lg"
                  className="w-24 h-24 rounded-full bg-purple-500 hover:bg-purple-600 border-2 border-purple-400"
                >
                  <Check className="h-8 w-8 text-white" />
                </Button>
              </div>

              {/* Swipe Instructions */}
              <div className="text-center mt-4 text-sm text-muted-foreground">
                <p>Swipe left to skip • Swipe right to start</p>
              </div>
            </div>
          </div>
        ) : (
          /* Results Summary */
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="text-center flex items-center justify-center gap-2">
                <Bot className="h-6 w-6" />
                Review Complete!
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center">
                <p className="text-muted-foreground mb-4">
                  You&apos;ve reviewed all available trading bots. Here&apos;s your
                  selection:
                </p>
              </div>

              <div className="space-y-3">
                {Object.entries(swipedCards).map(([botId, action]) => {
                  const bot = botCards.find((b) => b.id === botId);
                  if (!bot) return null;

                  return (
                    <div
                      key={botId}
                      className="flex items-center justify-between p-3 rounded-lg border"
                    >
                      <div className="flex items-center gap-3">
                        <div
                          className={`p-2 rounded-full ${bot.gradient.replace("from-", "bg-").split(" ")[0]}`}
                        >
                          {bot.icon}
                        </div>
                        <div>
                          <div className="font-medium">{bot.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {bot.strategy}
                          </div>
                        </div>
                      </div>
                      <Badge
                        variant={action === "start" ? "default" : "secondary"}
                      >
                        {action === "start" ? "Started" : "Skipped"}
                      </Badge>
                    </div>
                  );
                })}
              </div>

              <div className="flex gap-3 pt-4">
                <Button
                  onClick={resetCards}
                  variant="outline"
                  className="flex-1"
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Review Again
                </Button>
                <Button className="flex-1">
                  <Bot className="h-4 w-4 mr-2" />
                  Manage Bots
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </>
  );
}
