"use client";

import { useState } from "react";
import { type LucideIcon } from "lucide-react";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { NewTokenSearchModal } from "@/components/sidebar/new-token-search-modal";
import { TokenDetailsView } from "@/components/sidebar/token-details-view";
import { CoinGeckoToken } from "@/lib/coingecko-service";

export function NavSearch({
  items,
}: {
  items: {
    title: string;
    url: string;
    icon?: LucideIcon;
    isActive?: boolean;
    items?: {
      title: string;
      url: string;
    }[];
  }[];
}) {
  const [showSearchModal, setShowSearchModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedToken, setSelectedToken] = useState<CoinGeckoToken | null>(
    null,
  );

  const handleTokenSelect = (token: CoinGeckoToken) => {
    setSelectedToken(token);
    setShowSearchModal(false);
    setShowDetailsModal(true);
  };

  const handleSearchClick = (item: {
    title: string;
    url: string;
    icon?: LucideIcon;
    isActive?: boolean;
  }) => {
    if (item.title === "SEARCH") {
      setShowSearchModal(true);
    } else {
      window.location.href = item.url;
    }
  };

  return (
    <>
      <SidebarMenu className="mx-2">
        {items.map((item) => (
          <SidebarMenuItem key={item.title}>
            <SidebarMenuButton
              isActive={item.isActive}
              onClick={() => handleSearchClick(item)}
              className="cursor-pointer"
            >
              {item.icon && <item.icon className="w-4 h-4" />}
              <span className="ml-3 font-semibold">{item.title}</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        ))}
      </SidebarMenu>

      <NewTokenSearchModal
        isOpen={showSearchModal}
        onClose={() => setShowSearchModal(false)}
        onTokenSelect={(token) => {
          // Convert the new token format to CoinGecko format for compatibility
          const cgToken: CoinGeckoToken = {
            id: token.id,
            symbol: token.symbol,
            name: token.name,
            image: token.image,
            current_price: token.current_price || 0,
            market_cap: token.market_cap || 0,
            market_cap_rank: 0,
            fully_diluted_valuation: token.market_cap || 0,
            total_volume: token.volume_24h || 0,
            high_24h: 0,
            low_24h: 0,
            price_change_24h: 0,
            price_change_percentage_24h: token.price_change_24h || 0,
            market_cap_change_24h: 0,
            market_cap_change_percentage_24h: 0,
            circulating_supply: 0,
            total_supply: 0,
            max_supply: 0,
            ath: 0,
            ath_change_percentage: 0,
            ath_date: new Date().toISOString(),
            atl: 0,
            atl_change_percentage: 0,
            atl_date: new Date().toISOString(),
            roi: 0,
            last_updated: new Date().toISOString(),
          };
          handleTokenSelect(cgToken);
        }}
      />

      <TokenDetailsView
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        token={selectedToken}
      />
    </>
  );
}
