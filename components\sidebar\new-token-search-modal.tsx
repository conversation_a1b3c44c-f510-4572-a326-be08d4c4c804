"use client";

import React, { useState, useEffect, useMemo, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Search, TrendingUp, TrendingDown, Copy, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import {
  DexScreenerService,
  DexScreenerToken,
} from "@/lib/dexscreener-service";
import { CoinGeckoService, CoinGeckoToken } from "@/lib/coingecko-service";

// Helper functions for formatting
const formatPrice = (price: number) => {
  if (price < 0.01) {
    return `$${price.toFixed(6)}`;
  }
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(price);
};

const formatMarketCap = (marketCap: number) => {
  if (marketCap >= 1e12) return `$${(marketCap / 1e12).toFixed(2)}T`;
  if (marketCap >= 1e9) return `$${(marketCap / 1e9).toFixed(2)}B`;
  if (marketCap >= 1e6) return `$${(marketCap / 1e6).toFixed(2)}M`;
  return `$${marketCap.toLocaleString()}`;
};

// Optimized token interface
interface SearchToken {
  id: string;
  symbol: string;
  name: string;
  address: string;
  image: string;
  current_price: number;
  price_change_24h: number;
  volume_24h: number;
  market_cap: number;
  dex_id?: string;
  chain: string;
}

interface NewTokenSearchModalProps {
  isOpen: boolean;
  onClose: () => void;
  onTokenSelect?: (token: SearchToken) => void;
}

// Debounce hook for search optimization
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

export function NewTokenSearchModal({
  isOpen,
  onClose,
  onTokenSelect,
}: NewTokenSearchModalProps) {
  // Optimized state management
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<SearchToken[]>([]);
  const [trendingTokens, setTrendingTokens] = useState<SearchToken[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isLoadingTrending, setIsLoadingTrending] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);

  // Services
  const dexScreenerService = useMemo(() => new DexScreenerService(), []);
  const coinGeckoService = useMemo(() => new CoinGeckoService(), []);

  // Debounced search query
  const debouncedSearchQuery = useDebounce(searchQuery, 400);

  // Refs for cleanup
  const abortControllerRef = useRef<AbortController | null>(null);

  // Load trending tokens on mount
  useEffect(() => {
    if (isOpen && trendingTokens.length === 0) {
      loadTrendingTokens();
    }
  }, [isOpen]);

  // Load recent searches from localStorage
  useEffect(() => {
    if (isOpen) {
      try {
        const saved = localStorage.getItem("dextrip-recent-searches");
        if (saved) {
          setRecentSearches(JSON.parse(saved));
        }
      } catch (error) {
        console.error("Error loading recent searches:", error);
      }
    }
  }, [isOpen]);

  // Handle search when debounced query changes
  useEffect(() => {
    if (debouncedSearchQuery.trim()) {
      performSearch(debouncedSearchQuery);
    } else {
      setSearchResults([]);
      setIsSearching(false);
    }
  }, [debouncedSearchQuery]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const loadTrendingTokens = async () => {
    setIsLoadingTrending(true);
    try {
      console.log("📊 Loading trending tokens...");

      const dexTrending = await dexScreenerService.getTrendingTokens("solana");

      if (dexTrending.length > 0) {
        const convertedTrending = dexTrending
          .slice(0, 6) // Limit to 6 for better performance
          .map((token) => convertDexScreenerToken(token))
          .filter(
            (token, index, self) =>
              index === self.findIndex((t) => t.address === token.address),
          );

        setTrendingTokens(convertedTrending);
        console.log(`✅ Loaded ${convertedTrending.length} trending tokens`);
      } else {
        // Fallback to mock trending tokens
        setTrendingTokens(getMockTrendingTokens());
      }
    } catch (error) {
      console.error("Error loading trending tokens:", error);
      setTrendingTokens(getMockTrendingTokens());
    } finally {
      setIsLoadingTrending(false);
    }
  };

  const performSearch = async (query: string) => {
    // Cancel previous search
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();
    setIsSearching(true);

    try {
      console.log(`🔍 Searching for: "${query}"`);

      const dexResults = await dexScreenerService.enhancedSearch(query);

      if (dexResults.length > 0) {
        const convertedResults = dexResults
          .slice(0, 12) // Limit results for better performance
          .map((token) => convertDexScreenerToken(token))
          .filter(
            (token, index, self) =>
              index === self.findIndex((t) => t.address === token.address),
          );

        setSearchResults(convertedResults);
        console.log(`✅ Found ${convertedResults.length} search results`);
      } else {
        // Fallback to CoinGecko
        console.log("🔄 Trying CoinGecko fallback...");
        const searchResult = await coinGeckoService.searchCoins(query);

        if (searchResult.coins.length > 0) {
          const coinIds = searchResult.coins.slice(0, 8).map((coin) => coin.id);
          const marketData = await coinGeckoService.getCoinsMarket(coinIds);
          const convertedResults = marketData.map((token) =>
            convertCoinGeckoToken(token),
          );
          setSearchResults(convertedResults);
          console.log(
            `✅ CoinGecko fallback: ${convertedResults.length} results`,
          );
        } else {
          setSearchResults([]);
          console.log("❌ No results found");
        }
      }
    } catch (error) {
      if ((error as Error).name !== "AbortError") {
        console.error("Search error:", error);
        setSearchResults([]);
      }
    } finally {
      setIsSearching(false);
    }
  };

  const handleTokenClick = (token: SearchToken) => {
    // Add to recent searches
    const newRecent = [
      token.name,
      ...recentSearches.filter((s) => s !== token.name),
    ].slice(0, 5);
    setRecentSearches(newRecent);

    try {
      localStorage.setItem(
        "dextrip-recent-searches",
        JSON.stringify(newRecent),
      );
    } catch (error) {
      console.error("Error saving recent searches:", error);
    }

    if (onTokenSelect) {
      onTokenSelect(token);
    }
  };

  const handleClose = () => {
    setSearchQuery("");
    setSearchResults([]);
    onClose();
  };

  const copyTokenAddress = (token: SearchToken, event: React.MouseEvent) => {
    event.stopPropagation();
    navigator.clipboard.writeText(token.address);
    toast.success(`${token.symbol} address copied`);
  };

  // Helper functions
  const convertDexScreenerToken = (dexToken: DexScreenerToken): SearchToken => {
    const converted = dexScreenerService.convertToStandardFormat(dexToken);
    return {
      id: converted.address,
      symbol: converted.symbol,
      name: converted.name,
      address: converted.address,
      image: converted.image || converted.image_url || "",
      current_price: converted.current_price,
      price_change_24h: converted.price_change_24h,
      volume_24h: converted.volume_24h,
      market_cap: converted.market_cap,
      dex_id: converted.dex_id,
      chain: converted.chain,
    };
  };

  const convertCoinGeckoToken = (cgToken: CoinGeckoToken): SearchToken => {
    return {
      id: cgToken.id,
      symbol: cgToken.symbol,
      name: cgToken.name,
      address: cgToken.id, // Use ID as address for CoinGecko tokens
      image: cgToken.image,
      current_price: cgToken.current_price,
      price_change_24h: cgToken.price_change_percentage_24h,
      volume_24h: cgToken.total_volume,
      market_cap: cgToken.market_cap,
      chain: "ethereum",
    };
  };

  const getMockTrendingTokens = (): SearchToken[] => [
    {
      id: "bitcoin",
      symbol: "BTC",
      name: "Bitcoin",
      address: "bitcoin",
      image: "https://assets.coingecko.com/coins/images/1/small/bitcoin.png",
      current_price: 43250.5,
      price_change_24h: 2.45,
      volume_24h: 23456789012,
      market_cap: 847234567890,
      chain: "bitcoin",
    },
    {
      id: "ethereum",
      symbol: "ETH",
      name: "Ethereum",
      address: "ethereum",
      image: "https://assets.coingecko.com/coins/images/279/small/ethereum.png",
      current_price: 2650.75,
      price_change_24h: -1.23,
      volume_24h: 12345678901,
      market_cap: 318234567890,
      chain: "ethereum",
    },
    {
      id: "solana",
      symbol: "SOL",
      name: "Solana",
      address: "solana",
      image: "https://assets.coingecko.com/coins/images/4128/small/solana.png",
      current_price: 180.25,
      price_change_24h: 5.67,
      volume_24h: 3456789012,
      market_cap: 85234567890,
      chain: "solana",
    },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] p-0 overflow-hidden bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.2 }}
        >
          <DialogHeader className="p-6 pb-4">
            <DialogTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Search Tokens
            </DialogTitle>
          </DialogHeader>

          <div className="px-6 pb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by name, symbol, or address..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-10"
                autoFocus
              />
              {isSearching && (
                <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-muted-foreground" />
              )}
            </div>
          </div>

          <ScrollArea className="flex-1 max-h-[500px]">
            <div className="px-6 pb-6">
              <AnimatePresence mode="wait">
                {!searchQuery.trim() ? (
                  <motion.div
                    key="trending"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.2 }}
                  >
                    {/* Recent Searches */}
                    {recentSearches.length > 0 && (
                      <div className="mb-6">
                        <h3 className="text-sm font-medium mb-3">
                          Recent Searches
                        </h3>
                        <div className="flex flex-wrap gap-2">
                          {recentSearches.map((search, index) => (
                            <Button
                              key={index}
                              variant="outline"
                              size="sm"
                              onClick={() => setSearchQuery(search)}
                              className="h-8 text-xs"
                            >
                              {search}
                            </Button>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Trending Tokens */}
                    <div>
                      <h3 className="text-sm font-medium mb-3 flex items-center gap-2">
                        <TrendingUp className="h-4 w-4" />
                        Trending Tokens
                      </h3>
                      {isLoadingTrending ? (
                        <div className="space-y-3">
                          {[...Array(3)].map((_, i) => (
                            <div
                              key={i}
                              className="flex items-center space-x-3 p-3 animate-pulse"
                            >
                              <div className="h-10 w-10 bg-muted rounded-full" />
                              <div className="flex-1 space-y-2">
                                <div className="h-4 bg-muted rounded w-24" />
                                <div className="h-3 bg-muted rounded w-16" />
                              </div>
                              <div className="space-y-2">
                                <div className="h-4 bg-muted rounded w-20" />
                                <div className="h-3 bg-muted rounded w-16" />
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {trendingTokens.map((token) => (
                            <TokenItem
                              key={token.id}
                              token={token}
                              onClick={() => handleTokenClick(token)}
                              onCopyAddress={(e) => copyTokenAddress(token, e)}
                            />
                          ))}
                        </div>
                      )}
                    </div>
                  </motion.div>
                ) : (
                  <motion.div
                    key="results"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.2 }}
                  >
                    <h3 className="text-sm font-medium mb-3">
                      Search Results
                      {searchResults.length > 0 && ` (${searchResults.length})`}
                    </h3>

                    {isSearching ? (
                      <div className="space-y-3">
                        {[...Array(3)].map((_, i) => (
                          <div
                            key={i}
                            className="flex items-center space-x-3 p-3 animate-pulse"
                          >
                            <div className="h-10 w-10 bg-muted rounded-full" />
                            <div className="flex-1 space-y-2">
                              <div className="h-4 bg-muted rounded w-24" />
                              <div className="h-3 bg-muted rounded w-16" />
                            </div>
                            <div className="space-y-2">
                              <div className="h-4 bg-muted rounded w-20" />
                              <div className="h-3 bg-muted rounded w-16" />
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : searchResults.length > 0 ? (
                      <div className="space-y-2">
                        {searchResults.map((token) => (
                          <TokenItem
                            key={`${token.address}-${token.chain}`}
                            token={token}
                            onClick={() => handleTokenClick(token)}
                            onCopyAddress={(e) => copyTokenAddress(token, e)}
                          />
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No tokens found for &quot;{searchQuery}&quot;</p>
                        <p className="text-sm mt-1">
                          Try a different search term
                        </p>
                      </div>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </ScrollArea>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}

// Optimized TokenItem component with React.memo for performance
const TokenItem = React.memo(function TokenItem({
  token,
  onClick,
  onCopyAddress,
}: {
  token: SearchToken;
  onClick: () => void;
  onCopyAddress: (e: React.MouseEvent) => void;
}) {
  const isPositive = token.price_change_24h > 0;

  // Memoize formatted values to prevent recalculation
  const formattedPrice = useMemo(
    () => formatPrice(token.current_price),
    [token.current_price],
  );
  const formattedMarketCap = useMemo(
    () => formatMarketCap(token.market_cap),
    [token.market_cap],
  );
  const formattedPriceChange = useMemo(
    () => `${isPositive ? "+" : ""}${token.price_change_24h.toFixed(2)}%`,
    [token.price_change_24h, isPositive],
  );

  return (
    <motion.div
      whileHover={{ scale: 1.01 }}
      whileTap={{ scale: 0.99 }}
      className="flex items-center justify-between p-3 rounded-lg border hover:bg-muted/50 cursor-pointer transition-colors group"
      onClick={onClick}
    >
      <div className="flex items-center gap-3 flex-1 min-w-0">
        <div className="relative">
          <img
            src={token.image}
            alt={token.name}
            className="w-10 h-10 rounded-full"
            onError={(e) => {
              e.currentTarget.src = `https://via.placeholder.com/40x40/6366f1/ffffff?text=${token.symbol.charAt(
                0,
              )}`;
            }}
          />
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <h4 className="font-medium truncate">{token.name}</h4>
            <Badge variant="secondary" className="text-xs shrink-0">
              {token.symbol.toUpperCase()}
            </Badge>
          </div>
          <p className="text-sm text-muted-foreground truncate">
            {formattedMarketCap} • {token.chain}
          </p>
        </div>
      </div>

      <div className="text-right shrink-0">
        <p className="font-medium">{formattedPrice}</p>
        <div className="flex items-center gap-1 justify-end">
          {isPositive ? (
            <TrendingUp className="h-3 w-3 text-green-500" />
          ) : (
            <TrendingDown className="h-3 w-3 text-red-500" />
          )}
          <span
            className={cn(
              "text-sm",
              isPositive ? "text-green-500" : "text-red-500",
            )}
          >
            {formattedPriceChange}
          </span>
        </div>
      </div>

      <Button
        variant="ghost"
        size="sm"
        className="h-8 w-8 p-0 ml-2 opacity-0 group-hover:opacity-100 transition-opacity"
        onClick={onCopyAddress}
      >
        <Copy className="h-3 w-3" />
      </Button>
    </motion.div>
  );
});
