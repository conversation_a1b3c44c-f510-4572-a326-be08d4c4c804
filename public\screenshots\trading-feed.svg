
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:0.05" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#grad)"/>
  <rect x="40" y="40" width="720" height="520" rx="20" fill="none" stroke="#10b981" stroke-width="2" opacity="0.3"/>
  <text x="400" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="#10b981" opacity="0.7">Trading Feed</text>
  <text x="400" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#10b981" opacity="0.5">Screenshot Coming Soon</text>
</svg>
