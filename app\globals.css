@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.15 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0 0);
  --primary: linear-gradient(
    135deg,
    #0ca0d0 0%,
    #4349e1 25%,
    #6934c9 50%,
    #db2f83 75%,
    #f3661b 100%
  );
  --primary-solid: #6934c9;
  --primary-foreground: oklch(0.98 0 0);
  --secondary: oklch(0.95 0 0);
  --secondary-foreground: oklch(0.15 0 0);
  --muted: oklch(0.95 0 0);
  --muted-foreground: oklch(0.45 0 0);
  --accent: oklch(0.95 0 0);
  --accent-foreground: oklch(0.15 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.9 0 0);
  --input: oklch(0.9 0 0);
  --ring: linear-gradient(
    135deg,
    #0ca0d0 0%,
    #4349e1 25%,
    #6934c9 50%,
    #db2f83 75%,
    #f3661b 100%
  );
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(1 0 0);
  --sidebar-foreground: oklch(0.15 0 0);
  --sidebar-primary: linear-gradient(
    135deg,
    #0ca0d0 0%,
    #4349e1 25%,
    #6934c9 50%,
    #db2f83 75%,
    #f3661b 100%
  );
  --sidebar-primary-solid: #6934c9;
  --sidebar-primary-foreground: oklch(0.98 0 0);
  --sidebar-accent: oklch(0.95 0 0);
  --sidebar-accent-foreground: oklch(0.15 0 0);
  --sidebar-border: oklch(0.9 0 0);
  --sidebar-ring: linear-gradient(
    135deg,
    #0ca0d0 0%,
    #4349e1 25%,
    #6934c9 50%,
    #db2f83 75%,
    #f3661b 100%
  );
}

.dark {
  --background: oklch(0.08 0 0);
  --foreground: oklch(0.95 0 0);
  --card: oklch(0.12 0 0);
  --card-foreground: oklch(0.95 0 0);
  --popover: oklch(0.12 0 0);
  --popover-foreground: oklch(0.95 0 0);
  --primary: linear-gradient(
    135deg,
    #0ca0d0 0%,
    #4349e1 25%,
    #6934c9 50%,
    #db2f83 75%,
    #f3661b 100%
  );
  --primary-solid: #6934c9;
  --primary-foreground: oklch(0.08 0 0);
  --secondary: oklch(0.18 0 0);
  --secondary-foreground: oklch(0.95 0 0);
  --muted: oklch(0.18 0 0);
  --muted-foreground: oklch(0.65 0 0);
  --accent: oklch(0.18 0 0);
  --accent-foreground: oklch(0.95 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(0.25 0 0);
  --input: oklch(0.25 0 0);
  --ring: linear-gradient(
    135deg,
    #0ca0d0 0%,
    #4349e1 25%,
    #6934c9 50%,
    #db2f83 75%,
    #f3661b 100%
  );
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.12 0 0);
  --sidebar-foreground: oklch(0.95 0 0);
  --sidebar-primary: linear-gradient(
    135deg,
    #0ca0d0 0%,
    #4349e1 25%,
    #6934c9 50%,
    #db2f83 75%,
    #f3661b 100%
  );
  --sidebar-primary-solid: #6934c9;
  --sidebar-primary-foreground: oklch(0.08 0 0);
  --sidebar-accent: oklch(0.18 0 0);
  --sidebar-accent-foreground: oklch(0.95 0 0);
  --sidebar-border: oklch(0.25 0 0);
  --sidebar-ring: linear-gradient(
    135deg,
    #0ca0d0 0%,
    #4349e1 25%,
    #6934c9 50%,
    #db2f83 75%,
    #f3661b 100%
  );
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-gray-800 rounded-full;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-gray-600 rounded-full hover:bg-gray-500;
  }

  /* Gradient backgrounds */
  .gradient-bg {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  .gradient-border {
    background: linear-gradient(
      135deg,
      #0ca0d0,
      #4349e1,
      #6934c9,
      #db2f83,
      #f3661b
    );
    padding: 1px;
    border-radius: 0.5rem;
  }

  .gradient-border-inner {
    @apply bg-gray-900 rounded-lg;
  }

  /* Primary gradient utilities */
  .bg-primary-gradient {
    background: linear-gradient(
      135deg,
      #0ca0d0 0%,
      #4349e1 25%,
      #6934c9 50%,
      #db2f83 75%,
      #f3661b 100%
    );
  }

  .dark .bg-primary-gradient {
    background: linear-gradient(
      135deg,
      #0ca0d0 0%,
      #4349e1 25%,
      #6934c9 50%,
      #db2f83 75%,
      #f3661b 100%
    );
  }

  .text-primary-gradient {
    background: linear-gradient(
      135deg,
      #0ca0d0 0%,
      #4349e1 25%,
      #6934c9 50%,
      #db2f83 75%,
      #f3661b 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .dark .text-primary-gradient {
    background: linear-gradient(
      135deg,
      #0ca0d0 0%,
      #4349e1 25%,
      #6934c9 50%,
      #db2f83 75%,
      #f3661b 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .border-primary-gradient {
    border: 2px solid transparent;
    background: linear-gradient(
        135deg,
        #0ca0d0 0%,
        #4349e1 25%,
        #6934c9 50%,
        #db2f83 75%,
        #f3661b 100%
      )
      border-box;
    -webkit-mask:
      linear-gradient(#fff 0 0) padding-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: destination-out;
    mask:
      linear-gradient(#fff 0 0) padding-box,
      linear-gradient(#fff 0 0);
    mask-composite: exclude;
  }

  .dark .border-primary-gradient {
    background: linear-gradient(
        135deg,
        #0ca0d0 0%,
        #4349e1 25%,
        #6934c9 50%,
        #db2f83 75%,
        #f3661b 100%
      )
      border-box;
  }

  /* Pulse animation for active indicators */
  .pulse-green {
    animation: pulse-green 2s infinite;
  }

  @keyframes pulse-green {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  /* Glow effects */
  .glow-blue {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .glow-green {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
  }

  .glow-red {
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
  }

  /* Trading card hover effects */
  .trading-card {
    transition: all 0.3s ease;
    transform-style: preserve-3d;
  }

  .trading-card:hover {
    transform: translateY(-5px) rotateX(5deg);
  }

  /* Number counter animation */
  .number-counter {
    font-variant-numeric: tabular-nums;
  }

  /* Sonner toaster customization */
  .toaster {
    --normal-bg: var(--popover);
    --normal-text: var(--popover-foreground);
    --normal-border: var(--border);
    --success-bg: var(--primary);
    --success-text: var(--primary-foreground);
    --error-bg: var(--destructive);
    --error-text: var(--destructive-foreground);
  }

  .toaster [data-sonner-toast] {
    border-radius: var(--radius);
    border: 1px solid var(--border);
    backdrop-filter: blur(8px);
  }

  .toaster [data-sonner-toast][data-type="success"] {
    background: var(--primary);
    color: var(--primary-foreground);
    border-color: var(--primary);
  }

  .toaster [data-sonner-toast][data-type="error"] {
    background: var(--destructive);
    color: var(--destructive-foreground);
    border-color: var(--destructive);
  }
}
