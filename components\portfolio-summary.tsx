"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  PieChart,
  RefreshCw,
  ExternalLink,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface PortfolioHolding {
  id: string;
  symbol: string;
  name: string;
  image: string;
  amount: number;
  currentPrice: number;
  totalValue: number;
  pnlPercentage: number;
}

interface PortfolioSummaryProps {
  limit?: number;
  showHeader?: boolean;
}

export function PortfolioSummary({
  limit = 5,
  showHeader = true,
}: PortfolioSummaryProps) {
  const [holdings, setHoldings] = useState<PortfolioHolding[]>([]);
  const [totalValue, setTotalValue] = useState(0);
  const [totalPnL, setTotalPnL] = useState(0);
  const [loading, setLoading] = useState(false);

  const formatPrice = (price: number) => {
    if (price < 0.01) {
      return `$${price.toFixed(6)}`;
    } else if (price < 1) {
      return `$${price.toFixed(4)}`;
    } else {
      return `$${price.toFixed(2)}`;
    }
  };



  const loadPortfolioData = () => {
    setLoading(true);
    
    // Sample portfolio data - in a real app, this would come from an API or local storage
    const sampleHoldings: PortfolioHolding[] = [
      {
        id: "1",
        symbol: "BTC",
        name: "Bitcoin",
        image: "https://assets.coingecko.com/coins/images/1/large/bitcoin.png",
        amount: 0.5,
        currentPrice: 47500,
        totalValue: 23750,
        pnlPercentage: 5.56,
      },
      {
        id: "2",
        symbol: "ETH",
        name: "Ethereum",
        image: "https://assets.coingecko.com/coins/images/279/large/ethereum.png",
        amount: 2.5,
        currentPrice: 2650,
        totalValue: 6625,
        pnlPercentage: -5.36,
      },
      {
        id: "3",
        symbol: "SOL",
        name: "Solana",
        image: "https://assets.coingecko.com/coins/images/4128/large/solana.png",
        amount: 10,
        currentPrice: 135,
        totalValue: 1350,
        pnlPercentage: 12.5,
      },
      {
        id: "4",
        symbol: "ADA",
        name: "Cardano",
        image: "https://assets.coingecko.com/coins/images/975/large/cardano.png",
        amount: 1000,
        currentPrice: 0.45,
        totalValue: 450,
        pnlPercentage: -8.2,
      },
      {
        id: "5",
        symbol: "DOT",
        name: "Polkadot",
        image: "https://assets.coingecko.com/coins/images/12171/large/polkadot.png",
        amount: 50,
        currentPrice: 6.8,
        totalValue: 340,
        pnlPercentage: 15.3,
      },
    ];

    const limitedHoldings = sampleHoldings.slice(0, limit);
    const total = limitedHoldings.reduce((sum, holding) => sum + holding.totalValue, 0);
    const avgPnL = limitedHoldings.reduce((sum, holding) => sum + holding.pnlPercentage, 0) / limitedHoldings.length;

    setHoldings(limitedHoldings);
    setTotalValue(total);
    setTotalPnL(avgPnL);
    setLoading(false);
  };

  useEffect(() => {
    loadPortfolioData();
  }, [limit]);

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            <span>Loading portfolio...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      {showHeader && (
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              Portfolio Summary
              <Badge variant="secondary">{holdings.length}</Badge>
            </CardTitle>
            <div className="flex gap-2">
              <Button onClick={loadPortfolioData} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Link href="/portfolio">
                <Button variant="outline" size="sm">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View All
                </Button>
              </Link>
            </div>
          </div>
          
          {/* Portfolio Stats */}
          <div className="grid grid-cols-2 gap-4 mt-4">
            <div className="p-3 rounded-lg bg-muted/50">
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Total Value</span>
              </div>
              <div className="text-xl font-bold mt-1">{formatPrice(totalValue)}</div>
            </div>
            <div className="p-3 rounded-lg bg-muted/50">
              <div className="flex items-center gap-2">
                {totalPnL >= 0 ? (
                  <TrendingUp className="h-4 w-4 text-green-600" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-600" />
                )}
                <span className="text-sm font-medium">Avg P&L</span>
              </div>
              <div className={`text-xl font-bold mt-1 ${totalPnL >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {totalPnL >= 0 ? '+' : ''}{totalPnL.toFixed(2)}%
              </div>
            </div>
          </div>
        </CardHeader>
      )}

      <CardContent className={showHeader ? "" : "p-4"}>
        {holdings.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-4">No portfolio holdings</p>
            <Link href="/portfolio">
              <Button>
                <PieChart className="h-4 w-4 mr-2" />
                Create Portfolio
              </Button>
            </Link>
          </div>
        ) : (
          <div className="space-y-3">
            {holdings.map((holding) => (
              <div
                key={holding.id}
                className="flex items-center gap-3 p-3 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                <Image
                  src={holding.image}
                  alt={holding.name}
                  width={32}
                  height={32}
                  className="rounded-full"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = `https://api.dicebear.com/7.x/identicon/svg?seed=${holding.symbol}`;
                  }}
                />
                
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm truncate">{holding.name}</div>
                  <div className="text-xs text-muted-foreground">{holding.symbol}</div>
                </div>

                <div className="text-right">
                  <div className="font-medium text-sm">{formatPrice(holding.totalValue)}</div>
                  <div className={`text-xs ${holding.pnlPercentage >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {holding.pnlPercentage >= 0 ? '+' : ''}{holding.pnlPercentage.toFixed(2)}%
                  </div>
                </div>
              </div>
            ))}
            
            {holdings.length >= limit && (
              <div className="pt-2 border-t">
                <Link href="/portfolio">
                  <Button variant="outline" className="w-full" size="sm">
                    View All Holdings
                    <ExternalLink className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
