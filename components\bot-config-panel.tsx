"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Settings,
  Save,
  RotateCcw,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Bot, BotConfig } from "@/lib/utils";
import { toast } from "sonner";

interface BotConfigPanelProps {
  bot: Bot;
  onUpdateConfig: (botId: string, config: BotConfig) => void;
}

export function BotConfigPanel({ bot, onUpdateConfig }: BotConfigPanelProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [config, setConfig] = useState<BotConfig>(bot.config);
  const [hasChanges, setHasChanges] = useState(false);

  const handleConfigChange = (
    field: keyof BotConfig,
    value: string | number,
  ) => {
    setConfig((prev) => ({
      ...prev,
      [field]: value,
    }));
    setHasChanges(true);
  };

  const handleRSIChange = (type: "buy" | "sell", value: number) => {
    setConfig((prev) => ({
      ...prev,
      rsiThreshold: {
        ...prev.rsiThreshold!,
        [type]: value,
      },
    }));
    setHasChanges(true);
  };

  const handleSave = () => {
    onUpdateConfig(bot.id, config);
    setHasChanges(false);
    toast.success("Bot configuration updated successfully!");
  };

  const handleReset = () => {
    setConfig(bot.config);
    setHasChanges(false);
    toast.success("Configuration reset to saved values");
  };

  const getStrategyColor = (strategy: string) => {
    switch (strategy) {
      case "RSI":
        return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "MOMENTUM":
        return "bg-green-500/20 text-green-400 border-green-500/30";
      case "AI_TREND":
        return "bg-purple-500/20 text-purple-400 border-purple-500/30";
      case "SCALPING":
        return "bg-orange-500/20 text-orange-400 border-orange-500/30";
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-white flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Bot Configuration
            {hasChanges && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="w-2 h-2 bg-yellow-400 rounded-full"
              />
            )}
          </CardTitle>

          <div className="flex items-center gap-2">
            <Badge className={getStrategyColor(config.strategy)}>
              {config.strategy}
            </Badge>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-gray-400 hover:text-white"
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </CardHeader>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <CardContent className="space-y-6">
              {/* Basic Settings */}
              <div className="space-y-4">
                <h4 className="text-white font-medium">Basic Settings</h4>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm text-gray-400 mb-2 block">
                      Stop Loss (%)
                    </label>
                    <Input
                      type="number"
                      step="0.1"
                      min="0"
                      max="50"
                      value={config.stopLoss}
                      onChange={(e) =>
                        handleConfigChange(
                          "stopLoss",
                          parseFloat(e.target.value) || 0,
                        )
                      }
                      className="bg-gray-800 border-gray-700 text-white"
                      disabled={bot.isActive}
                    />
                  </div>

                  <div>
                    <label className="text-sm text-gray-400 mb-2 block">
                      Take Profit (%)
                    </label>
                    <Input
                      type="number"
                      step="0.1"
                      min="0"
                      max="100"
                      value={config.takeProfit}
                      onChange={(e) =>
                        handleConfigChange(
                          "takeProfit",
                          parseFloat(e.target.value) || 0,
                        )
                      }
                      className="bg-gray-800 border-gray-700 text-white"
                      disabled={bot.isActive}
                    />
                  </div>

                  <div>
                    <label className="text-sm text-gray-400 mb-2 block">
                      Max Slippage (%)
                    </label>
                    <Input
                      type="number"
                      step="0.1"
                      min="0"
                      max="10"
                      value={config.maxSlippage}
                      onChange={(e) =>
                        handleConfigChange(
                          "maxSlippage",
                          parseFloat(e.target.value) || 0,
                        )
                      }
                      className="bg-gray-800 border-gray-700 text-white"
                      disabled={bot.isActive}
                    />
                  </div>

                  <div>
                    <label className="text-sm text-gray-400 mb-2 block">
                      Min Liquidity (SOL)
                    </label>
                    <Input
                      type="number"
                      step="1"
                      min="0"
                      value={config.minLiquidity}
                      onChange={(e) =>
                        handleConfigChange(
                          "minLiquidity",
                          parseFloat(e.target.value) || 0,
                        )
                      }
                      className="bg-gray-800 border-gray-700 text-white"
                      disabled={bot.isActive}
                    />
                  </div>
                </div>
              </div>

              {/* Strategy-Specific Settings */}
              {config.strategy === "RSI" && config.rsiThreshold && (
                <div className="space-y-4">
                  <h4 className="text-white font-medium">RSI Settings</h4>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm text-gray-400 mb-2 block">
                        RSI Buy Threshold
                      </label>
                      <Input
                        type="number"
                        min="0"
                        max="100"
                        value={config.rsiThreshold.buy}
                        onChange={(e) =>
                          handleRSIChange("buy", parseInt(e.target.value) || 0)
                        }
                        className="bg-gray-800 border-gray-700 text-white"
                        disabled={bot.isActive}
                      />
                    </div>

                    <div>
                      <label className="text-sm text-gray-400 mb-2 block">
                        RSI Sell Threshold
                      </label>
                      <Input
                        type="number"
                        min="0"
                        max="100"
                        value={config.rsiThreshold.sell}
                        onChange={(e) =>
                          handleRSIChange("sell", parseInt(e.target.value) || 0)
                        }
                        className="bg-gray-800 border-gray-700 text-white"
                        disabled={bot.isActive}
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Advanced Settings */}
              <div className="space-y-4">
                <h4 className="text-white font-medium">Advanced Settings</h4>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm text-gray-400 mb-2 block">
                      Timeframe
                    </label>
                    <select
                      value={config.timeframe}
                      onChange={(e) =>
                        handleConfigChange("timeframe", e.target.value)
                      }
                      className="w-full h-9 px-3 py-1 bg-gray-800 border border-gray-700 text-white rounded-md text-sm"
                      disabled={bot.isActive}
                    >
                      <option value="1m">1 Minute</option>
                      <option value="5m">5 Minutes</option>
                      <option value="15m">15 Minutes</option>
                      <option value="1h">1 Hour</option>
                    </select>
                  </div>

                  <div>
                    <label className="text-sm text-gray-400 mb-2 block">
                      Max Positions
                    </label>
                    <Input
                      type="number"
                      min="1"
                      max="20"
                      value={config.maxPositions}
                      onChange={(e) =>
                        handleConfigChange(
                          "maxPositions",
                          parseInt(e.target.value) || 1,
                        )
                      }
                      className="bg-gray-800 border-gray-700 text-white"
                      disabled={bot.isActive}
                    />
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              {hasChanges && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex gap-2 pt-4 border-t border-gray-800"
                >
                  <Button
                    onClick={handleSave}
                    disabled={bot.isActive}
                    className="flex-1 bg-green-600 hover:bg-green-700"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                  </Button>

                  <Button
                    onClick={handleReset}
                    variant="outline"
                    className="border-gray-700 text-gray-300 hover:text-white"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Reset
                  </Button>
                </motion.div>
              )}

              {bot.isActive && (
                <div className="text-sm text-yellow-400 bg-yellow-400/10 border border-yellow-400/20 rounded-lg p-3">
                  ⚠️ Bot configuration cannot be changed while the bot is
                  active. Stop the bot to make changes.
                </div>
              )}
            </CardContent>
          </motion.div>
        )}
      </AnimatePresence>
    </Card>
  );
}
