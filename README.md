# Welcome to your Supabase + Next.js + Clerk app

This is a Next.js project using Supabase as the database backend.

After the initial setup you'll have a working full-stack app using:

- [Supabase](https://supabase.com/) as your backend (database, authentication, real-time)
- [React](https://react.dev/) as your frontend (web page interactivity)
- [Next.js](https://nextjs.org/) for optimized web hosting and page routing
- [Tailwind](https://tailwindcss.com/) for building great looking accessible UI
- [Clerk](https://clerk.com/) for authentication

## Get started

1. Clone this repository and install dependencies:

```bash
npm install
```

2. Set up Supabase:
   - Create a new project at [supabase.com](https://supabase.com)
   - Copy your project URL and anon key
   - Run the SQL commands from `supabase-schema.sql` in your Supabase SQL editor

3. Set up environment variables:
   - Copy `.env.local` and fill in your Supabase credentials
   - Add your Clerk credentials (if using <PERSON> for authentication)

4. Run the development server:

```bash
npm run dev
```

## Environment Variables

Create a `.env.local` file with the following variables:

```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
CLERK_SECRET_KEY=your_clerk_secret_key
```

## Learn more

To learn more about the technologies used in this project:

- [Supabase Documentation](https://supabase.com/docs) - Learn about Supabase features and APIs
- [Next.js Documentation](https://nextjs.org/docs) - Learn about Next.js features and API
- [Clerk Documentation](https://clerk.com/docs) - Learn about Clerk authentication
- [Tailwind CSS](https://tailwindcss.com/docs) - Learn about utility-first CSS framework

## Database Schema

The project includes a `supabase-schema.sql` file with the database schema. Make sure to run these SQL commands in your Supabase project to set up the required tables and policies.
