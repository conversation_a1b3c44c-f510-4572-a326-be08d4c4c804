import { NextRequest, NextResponse } from "next/server";
import { realTimeDataService } from "@/lib/real-time-data";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const source = searchParams.get("source") || "database";
    const limit = parseInt(searchParams.get("limit") || "50");

    if (source === "database") {
      // Get coins from real-time data service
      const coins = await realTimeDataService.getNewListings("solana", "all", limit);
      
      return NextResponse.json({
        success: true,
        coins,
        count: coins.length,
        source: "realtime-service",
      });
    }

    return NextResponse.json({
      success: false,
      error: "Invalid source parameter",
    }, { status: 400 });
  } catch (error) {
    console.error("Error fetching real-time coins:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch real-time coins" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, chain = "solana", limit = 50 } = body;

    switch (action) {
      case "sync_listings":
        // Force refresh listings from external APIs
        const listings = await realTimeDataService.forceRefresh(chain, "all", limit);
        return NextResponse.json({
          success: true,
          message: "Listings synced successfully",
          count: listings.length,
        });

      case "sync_trending":
        // This would sync trending tokens - for now just return success
        return NextResponse.json({
          success: true,
          message: "Trending tokens sync initiated",
        });

      case "clear_cache":
        // Clear cache and force refresh
        realTimeDataService.clearCache();
        return NextResponse.json({
          success: true,
          message: "Cache cleared successfully",
        });

      default:
        return NextResponse.json(
          { success: false, error: "Invalid action parameter" },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("Error syncing real-time data:", error);
    return NextResponse.json(
      { success: false, error: "Failed to sync real-time data" },
      { status: 500 }
    );
  }
}
