"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  TrendingUp,
  TrendingDown,
  ExternalLink,
  RefreshCw,
  Search,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";

interface CoinGeckoCoin {
  id: string;
  symbol: string;
  name: string;
  image: string;
  current_price: number;
  market_cap: number;
  market_cap_rank: number;
  fully_diluted_valuation: number;
  total_volume: number;
  high_24h: number;
  low_24h: number;
  price_change_24h: number;
  price_change_percentage_24h: number;
  market_cap_change_24h: number;
  market_cap_change_percentage_24h: number;
  circulating_supply: number;
  total_supply: number;
  max_supply: number;
  ath: number;
  ath_change_percentage: number;
  ath_date: string;
  atl: number;
  atl_change_percentage: number;
  atl_date: string;
  last_updated: string;
}

interface CoinGeckoCoinListProps {
  limit?: number;
  showHeader?: boolean;
  showPagination?: boolean;
}

export function CoinGeckoCoinList({
  limit = 100,
  showHeader = true,
  showPagination = true,
}: CoinGeckoCoinListProps) {
  const [coins, setCoins] = useState<CoinGeckoCoin[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState("market_cap_desc");

  const formatPrice = (price: number) => {
    if (price < 0.01) {
      return `$${price.toFixed(6)}`;
    } else if (price < 1) {
      return `$${price.toFixed(4)}`;
    } else {
      return `$${price.toFixed(2)}`;
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1e12) return `${(num / 1e12).toFixed(2)}T`;
    if (num >= 1e9) return `${(num / 1e9).toFixed(2)}B`;
    if (num >= 1e6) return `${(num / 1e6).toFixed(2)}M`;
    if (num >= 1e3) return `${(num / 1e3).toFixed(2)}K`;
    return num.toFixed(2);
  };

  const fetchCoins = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(
        `/api/proxy/coingecko?endpoint=markets&limit=${limit}&page=${page}&order=${sortBy}`,
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch coins: ${response.status}`);
      }

      const data = await response.json();
      setCoins(data || []);
    } catch (err) {
      console.error("Error fetching CoinGecko coins:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch coins");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCoins();
  }, [limit, page, sortBy]);

  const filteredCoins = coins.filter(
    (coin) =>
      coin.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      coin.symbol.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            <span>Loading CoinGecko coins...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <p className="text-red-500 mb-4">Error: {error}</p>
            <Button onClick={fetchCoins} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      {showHeader && (
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              🦎 CoinGecko Top Coins
              <Badge variant="secondary">{filteredCoins.length}</Badge>
            </CardTitle>
            <Button onClick={fetchCoins} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
          
          {/* Search and Sort Controls */}
          <div className="flex gap-4 mt-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search coins..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="market_cap_desc">Market Cap (High to Low)</SelectItem>
                <SelectItem value="market_cap_asc">Market Cap (Low to High)</SelectItem>
                <SelectItem value="volume_desc">Volume (High to Low)</SelectItem>
                <SelectItem value="price_change_percentage_24h_desc">24h Change (High to Low)</SelectItem>
                <SelectItem value="price_change_percentage_24h_asc">24h Change (Low to High)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
      )}

      <CardContent className={showHeader ? "" : "p-0"}>
        {filteredCoins.length === 0 ? (
          <p className="text-gray-400 text-center py-8">
            No coins found matching your search
          </p>
        ) : (
          <div className="space-y-2">
            {filteredCoins.map((coin, index) => (
              <div
                key={coin.id}
                className="flex items-center gap-4 p-4 rounded-lg border hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer"
                onClick={() => window.open(`https://www.coingecko.com/en/coins/${coin.id}`, '_blank')}
              >
                {/* Rank */}
                <div className="text-sm text-gray-500 w-8">
                  #{coin.market_cap_rank || (page - 1) * limit + index + 1}
                </div>

                {/* Coin Info */}
                <div className="flex items-center gap-3 flex-1">
                  <Image
                    src={coin.image}
                    alt={coin.name}
                    width={32}
                    height={32}
                    className="rounded-full"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = `https://api.dicebear.com/7.x/identicon/svg?seed=${coin.symbol}`;
                    }}
                  />
                  <div>
                    <div className="font-medium">{coin.name}</div>
                    <div className="text-sm text-gray-500 uppercase">{coin.symbol}</div>
                  </div>
                </div>

                {/* Price */}
                <div className="text-right">
                  <div className="font-medium">{formatPrice(coin.current_price)}</div>
                  <div
                    className={`text-sm flex items-center gap-1 ${
                      coin.price_change_percentage_24h >= 0
                        ? "text-green-600"
                        : "text-red-600"
                    }`}
                  >
                    {coin.price_change_percentage_24h >= 0 ? (
                      <TrendingUp className="h-3 w-3" />
                    ) : (
                      <TrendingDown className="h-3 w-3" />
                    )}
                    {Math.abs(coin.price_change_percentage_24h).toFixed(2)}%
                  </div>
                </div>

                {/* Market Cap */}
                <div className="text-right w-24">
                  <div className="text-sm text-gray-500">Market Cap</div>
                  <div className="font-medium">${formatNumber(coin.market_cap)}</div>
                </div>

                {/* Volume */}
                <div className="text-right w-24">
                  <div className="text-sm text-gray-500">Volume 24h</div>
                  <div className="font-medium">${formatNumber(coin.total_volume)}</div>
                </div>

                {/* External Link */}
                <Button variant="ghost" size="sm" className="p-2">
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {showPagination && (
          <div className="flex items-center justify-between mt-6 pt-4 border-t">
            <div className="text-sm text-gray-500">
              Page {page} • Showing {filteredCoins.length} coins
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(Math.max(1, page - 1))}
                disabled={page === 1}
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(page + 1)}
                disabled={filteredCoins.length < limit}
              >
                Next
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
