"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { motion } from "framer-motion";
import {
  ArrowLeft,
  TrendingUp,
  TrendingDown,
  Play,
  Pause,
  DollarSign,
  Target,
  BarChart3,
  Settings,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { useBots } from "@/contexts/bot-context";
import { Bot as BotType } from "@/lib/utils";
import { formatSOL, formatPercentage } from "@/lib/utils";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@radix-ui/react-separator";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { TradingTable } from "@/components/trading-table";
import { WatchingTable } from "@/components/watching-table";
import { BotConfigPanel } from "@/components/bot-config-panel";
import { BotAnalytics } from "@/components/bot-analytics";

export default function BotDetailPage() {
  const params = useParams();
  const router = useRouter();
  const botId = params.id as string;

  const { getBotById, getTradesForBot, startBot, stopBot, updateBotConfig } =
    useBots();
  const [bot, setBot] = useState<BotType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [, setShowConfigPanel] = useState(false);

  useEffect(() => {
    const foundBot = getBotById(botId);
    if (!foundBot) {
      router.push("/");
      return;
    }

    setBot(foundBot);
    setIsLoading(false);
  }, [botId, router, getBotById]);

  // Update bot state when context changes
  useEffect(() => {
    const updatedBot = getBotById(botId);
    if (updatedBot) {
      setBot(updatedBot);
    }
  }, [botId, getBotById]);

  const trades = getTradesForBot(botId);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="h-8 w-8 border-2 border-blue-500 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  if (!bot) {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-white mb-4">Bot Not Found</h1>
        <Button onClick={() => router.push("/")}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Button>
      </div>
    );
  }

  return (
    <div>
      <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 justify-between pr-6">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href="/bot">Dextrip Bot</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className="hidden md:block" />
              <BreadcrumbItem>
                <BreadcrumbPage>{bot.name}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowConfigPanel(true)}
          >
            <Settings className="h-4 w-4 mr-2" />
            Configure
          </Button>

          {bot.isActive ? (
            <Button
              onClick={() => stopBot(bot.id)}
              variant="destructive"
              size="sm"
            >
              <Pause className="h-4 w-4 mr-2" />
              Stop Bot
            </Button>
          ) : (
            <Button onClick={() => startBot(bot.id, bot.solPerTrade)} size="sm">
              <Play className="h-4 w-4 mr-2" />
              Start Bot
            </Button>
          )}
        </div>
      </header>
      <div className="h-px bg-border" />
      {/* Main Content */}
      <div className="space-y-6 p-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <TradingTable />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <WatchingTable />
        </motion.div>

        {/* Trade Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Trade History
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-3 text-sm font-medium text-muted-foreground">
                        Date
                      </th>
                      <th className="text-left p-3 text-sm font-medium text-muted-foreground">
                        Type
                      </th>
                      <th className="text-left p-3 text-sm font-medium text-muted-foreground">
                        Token
                      </th>
                      <th className="text-left p-3 text-sm font-medium text-muted-foreground">
                        Amount
                      </th>
                      <th className="text-left p-3 text-sm font-medium text-muted-foreground">
                        Price
                      </th>
                      <th className="text-left p-3 text-sm font-medium text-muted-foreground">
                        Total
                      </th>
                      <th className="text-left p-3 text-sm font-medium text-muted-foreground">
                        P&L
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {trades.length > 0 ? (
                      trades.slice(0, 10).map((trade) => (
                        <tr
                          key={trade.id}
                          className="border-b hover:bg-muted/50"
                        >
                          <td className="p-3 text-sm">
                            {new Date(trade.timestamp).toLocaleDateString()}{" "}
                            {new Date(trade.timestamp).toLocaleTimeString([], {
                              hour: "2-digit",
                              minute: "2-digit",
                            })}
                          </td>
                          <td className="p-3">
                            <span
                              className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                trade.type === "BUY"
                                  ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                                  : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                              }`}
                            >
                              {trade.type}
                            </span>
                          </td>
                          <td className="p-3 text-sm font-medium">
                            {trade.token}
                          </td>
                          <td className="p-3 text-sm">
                            {trade.amount.toLocaleString()}
                          </td>
                          <td className="p-3 text-sm">
                            ${trade.price.toFixed(6)}
                          </td>
                          <td className="p-3 text-sm font-medium">
                            {formatSOL(trade.amount * trade.price)}
                          </td>
                          <td className="p-3 text-sm">
                            {trade.pnl ? (
                              <span
                                className={
                                  trade.pnl >= 0
                                    ? "text-green-500"
                                    : "text-red-500"
                                }
                              >
                                {trade.pnl >= 0 ? "+" : ""}
                                {formatSOL(trade.pnl)}
                              </span>
                            ) : (
                              "-"
                            )}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td
                          colSpan={9}
                          className="p-8 text-center text-muted-foreground"
                        >
                          No trades yet. Start the bot to begin trading.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
              {trades.length > 10 && (
                <div className="mt-4 text-center">
                  <Button variant="outline" size="sm">
                    View All Trades ({trades.length})
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-4"
        >
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-500/10 rounded-lg">
                  <BarChart3 className="h-5 w-5 text-blue-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total Trades</p>
                  <p className="text-2xl font-bold">{bot.stats.totalTrades}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-500/10 rounded-lg">
                  <Target className="h-5 w-5 text-green-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Win Rate</p>
                  <p className="text-2xl font-bold">
                    {formatPercentage(bot.stats.winRate)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div
                  className={`p-2 rounded-lg ${
                    bot.stats.pnl >= 0 ? "bg-green-500/10" : "bg-red-500/10"
                  }`}
                >
                  {bot.stats.pnl >= 0 ? (
                    <TrendingUp className="h-5 w-5 text-green-500" />
                  ) : (
                    <TrendingDown className="h-5 w-5 text-red-500" />
                  )}
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total P&L</p>
                  <p
                    className={`text-2xl font-bold ${
                      bot.stats.pnl >= 0 ? "text-green-500" : "text-red-500"
                    }`}
                  >
                    {bot.stats.pnl >= 0 ? "+" : "-"}
                    {formatSOL(Math.abs(bot.stats.pnl))}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-500/10 rounded-lg">
                  <DollarSign className="h-5 w-5 text-purple-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">SOL per Trade</p>
                  <p className="text-2xl font-bold">
                    {formatSOL(bot.solPerTrade)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Bot Analytics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <BotAnalytics bot={bot} trades={trades} />
        </motion.div>

        {/* Configuration Panel */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <BotConfigPanel bot={bot} onUpdateConfig={updateBotConfig} />
        </motion.div>
      </div>
    </div>
  );
}
