"use client";

import React, { useState, useEffect } from "react";
// Image import removed - not used in this component
import Link from "next/link";
import {
  TrendingUp,
  TrendingDown,
  Bot,
  MoreHorizontal,
  ExternalLink,
  // Activity removed - not used
} from "lucide-react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { Badge } from "@/components/ui/badge";
import { useBots } from "@/contexts/bot-context";
import { formatSOL } from "@/lib/utils";
import { Card, CardContent } from "../ui/card";
import { TokenDetailsView } from "@/components/sidebar/token-details-view";
import { CoinGeckoToken } from "@/lib/coingecko-service";

interface CoinInTrade {
  id: string;
  token: string;
  botName: string;
  botId: string;
  pnl: number;
  entryPrice: number;
  currentPrice: number;
  amount: number;
  timestamp: Date;
  image_url?: string;
  name?: string;
}

interface CoinMetadata {
  image_url: string;
  name: string;
  symbol: string;
}

// Cache for coin metadata to avoid repeated API calls
const coinMetadataCache = new Map<string, CoinMetadata>();

// Function to fetch coin metadata from DexScreener
async function fetchCoinMetadata(
  tokenSymbol: string,
): Promise<CoinMetadata | null> {
  // Check cache first
  if (coinMetadataCache.has(tokenSymbol)) {
    return coinMetadataCache.get(tokenSymbol)!;
  }

  try {
    // Use our comprehensive token metadata API
    await fetch(
      `/api/tokens/metadata?symbol=${encodeURIComponent(tokenSymbol)}`,
    );

    // if (metadataResponse.ok) {
    //   const metadataData = await metadataResponse.json();
    //   if (metadataData.success && metadataData.token) {
    //     const token = metadataData.token;
    //     const metadata = {
    //       name: token.name,
    //       symbol: token.symbol,
    //       image:
    //         token.image ||
    //         `https://api.dicebear.com/7.x/identicon/svg?seed=${tokenSymbol}`,
    //       price: token.price || 0,
    //       priceChange24h: token.priceChange24h || 0,
    //     };
    //     coinMetadataCache.set(tokenSymbol, metadata);
    //     return metadata;
    //   }
    // }

    // If metadata API fails, fallback to DexScreener proxy
    const searchResponse = await fetch(
      `/api/proxy/dexscreener?q=${encodeURIComponent(tokenSymbol)}`,
    );

    if (!searchResponse.ok) {
      throw new Error(`Token search failed: ${searchResponse.status}`);
    }

    const searchData = await searchResponse.json();

    if (searchData.pairs && searchData.pairs.length > 0) {
      // Get the most liquid pair
      const bestPair = searchData.pairs.reduce(
        (
          best: { liquidity?: { usd?: number } },
          current: { liquidity?: { usd?: number } },
        ) => {
          const currentLiquidity = current.liquidity?.usd || 0;
          const bestLiquidity = best.liquidity?.usd || 0;
          return currentLiquidity > bestLiquidity ? current : best;
        },
      );

      const metadata: CoinMetadata = {
        image_url: bestPair.info?.imageUrl || "",
        name: bestPair.baseToken?.name || tokenSymbol,
        symbol: bestPair.baseToken?.symbol || tokenSymbol,
      };

      // Cache the result
      coinMetadataCache.set(tokenSymbol, metadata);
      return metadata;
    }

    // Fallback: try CoinGecko search
    const coinGeckoResponse = await fetch(
      `https://api.coingecko.com/api/v3/search?query=${encodeURIComponent(
        tokenSymbol,
      )}`,
    );

    if (coinGeckoResponse.ok) {
      const coinGeckoData = await coinGeckoResponse.json();

      if (coinGeckoData.coins && coinGeckoData.coins.length > 0) {
        const coin = coinGeckoData.coins[0];
        const metadata: CoinMetadata = {
          image_url: coin.large || coin.thumb || "",
          name: coin.name || tokenSymbol,
          symbol: coin.symbol || tokenSymbol,
        };

        coinMetadataCache.set(tokenSymbol, metadata);
        return metadata;
      }
    }

    return null;
  } catch (error) {
    console.error(`Failed to fetch metadata for ${tokenSymbol}:`, error);
    return null;
  }
}

export function NavCoin() {
  const { isMobile } = useSidebar();
  const { bots } = useBots();
  const [enrichedCoins, setEnrichedCoins] = useState<CoinInTrade[]>([]);
  const [showTokenDetails, setShowTokenDetails] = useState(false);
  const [selectedToken, setSelectedToken] = useState<CoinGeckoToken | null>(
    null,
  );

  // Get all active bots with current positions
  const coinsInTrade: CoinInTrade[] = React.useMemo(() => {
    return bots
      .filter((bot) => bot.isActive && bot.stats.currentPosition)
      .map((bot) => {
        const position = bot.stats.currentPosition!;
        return {
          id: `${bot.id}-${position.token}`,
          token: position.token,
          botName: bot.name,
          botId: bot.id,
          pnl: position.pnl,
          entryPrice: position.entryPrice,
          currentPrice: position.currentPrice,
          amount: position.amount,
          timestamp: position.timestamp,
          image_url: "", // Will be enriched with real data
          name: position.token, // Will be enriched with real name
        };
      });
  }, [bots]);

  // Enrich coins with metadata from APIs
  useEffect(() => {
    const enrichCoins = async () => {
      const enriched = await Promise.all(
        coinsInTrade.map(async (coin) => {
          const metadata = await fetchCoinMetadata(coin.token);
          return {
            ...coin,
            image_url: metadata?.image_url || "",
            name: metadata?.name || coin.token,
          };
        }),
      );
      setEnrichedCoins(enriched);
    };

    if (coinsInTrade.length > 0) {
      enrichCoins();
    } else {
      setEnrichedCoins([]);
    }
  }, [coinsInTrade]);

  const formatPrice = (price: number) => {
    if (price < 0.01) {
      return `$${price.toFixed(6)}`;
    } else if (price < 1) {
      return `$${price.toFixed(4)}`;
    } else {
      return `$${price.toFixed(2)}`;
    }
  };

  const formatAmount = (amount: number, token: string) => {
    if (amount >= 1e9) {
      return `${(amount / 1e9).toFixed(2)}B ${token}`;
    } else if (amount >= 1e6) {
      return `${(amount / 1e6).toFixed(2)}M ${token}`;
    } else if (amount >= 1e3) {
      return `${(amount / 1e3).toFixed(2)}K ${token}`;
    } else {
      return `${amount.toFixed(2)} ${token}`;
    }
  };

  const getPnLColor = (pnl: number) => {
    if (pnl > 0) return "text-green-500";
    if (pnl < 0) return "text-red-500";
    return "text-gray-500";
  };

  const getPnLIcon = (pnl: number) => {
    if (pnl > 0) return <TrendingUp className="w-3 h-3" />;
    if (pnl < 0) return <TrendingDown className="w-3 h-3" />;
    return null;
  };

  // Convert CoinInTrade to CoinGeckoToken format
  const convertToTokenDetails = (coin: CoinInTrade): CoinGeckoToken => {
    return {
      id: coin.token.toLowerCase(),
      symbol: coin.token,
      name: coin.name || coin.token,
      image:
        coin.image_url ||
        `https://via.placeholder.com/40x40/6366f1/ffffff?text=${coin.token.charAt(
          0,
        )}`,
      current_price: coin.currentPrice || 0,
      market_cap: 0, // Not available from trading data
      market_cap_rank: 0,
      fully_diluted_valuation: 0,
      total_volume: 0,
      high_24h: 0,
      low_24h: 0,
      price_change_24h: 0,
      price_change_percentage_24h:
        coin.pnl > 0 ? Math.abs(coin.pnl) : -Math.abs(coin.pnl),
      market_cap_change_24h: 0,
      market_cap_change_percentage_24h: 0,
      circulating_supply: 0,
      total_supply: 0,
      max_supply: 0,
      ath: 0,
      ath_change_percentage: 0,
      ath_date: new Date().toISOString(),
      atl: 0,
      atl_change_percentage: 0,
      atl_date: new Date().toISOString(),
      roi: coin.pnl,
      last_updated: new Date().toISOString(),
    };
  };

  // Handle coin click to open token details modal
  const handleCoinClick = (coin: CoinInTrade) => {
    const tokenDetails = convertToTokenDetails(coin);
    setSelectedToken(tokenDetails);
    setShowTokenDetails(true);
  };

  // Use enriched coins if available, otherwise use basic coins
  const displayCoins = enrichedCoins.length > 0 ? enrichedCoins : coinsInTrade;

  if (displayCoins.length === 0) {
    return (
      <SidebarGroup className="group-data-[collapsible=icon]:hidden">
        <SidebarGroupLabel>Trading</SidebarGroupLabel>
        <Card className="border-0">
          <CardContent>
            <div className="px-2 py-4 text-sm text-muted-foreground text-center">
              No active positions
            </div>
          </CardContent>
        </Card>
      </SidebarGroup>
    );
  }

  return (
    <SidebarGroup className="group-data-[collapsible=icon]:hidden">
      <SidebarGroupLabel>
        <div className="flex items-center justify-between w-full">
          <span>Trading</span>
          <Badge variant="secondary" className="text-xs">
            {displayCoins.length}
          </Badge>
        </div>
      </SidebarGroupLabel>
      <SidebarMenu>
        {displayCoins.map((coin) => (
          <SidebarMenuItem key={coin.id}>
            <SidebarMenuButton asChild>
              <button
                onClick={() => handleCoinClick(coin)}
                className="flex items-center gap-3 py-6 hover:bg-neutral-800/50 dark:hover:bg-gray-800 rounded-lg transition-colors w-full text-left"
              >
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <div className="min-w-0 flex-1">
                      <div className="font-semibold text-sm truncate">
                        {coin.token}
                      </div>
                      {/* {coin.name && coin.name !== coin.token && (
                        <div className="text-xs text-muted-foreground truncate">
                          {coin.name}
                        </div>
                      )} */}
                    </div>
                    <div
                      className={`text-xs font-medium flex items-center gap-1 ${getPnLColor(
                        coin.pnl,
                      )}`}
                    >
                      {getPnLIcon(coin.pnl)}
                      {formatSOL(coin.pnl)}
                    </div>
                  </div>
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <div className="truncate max-w-[100px]">
                      <Bot className="w-3 h-3 inline mr-1" />
                      {coin.botName}
                    </div>
                    <div className="text-right">
                      {formatPrice(coin.currentPrice)}
                    </div>
                  </div>
                </div>
              </button>
            </SidebarMenuButton>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuAction showOnHover>
                  <MoreHorizontal className="w-4 h-4" />
                  <span className="sr-only">More</span>
                </SidebarMenuAction>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-56 rounded-lg"
                side={isMobile ? "bottom" : "right"}
                align={isMobile ? "end" : "start"}
              >
                <DropdownMenuItem asChild>
                  <Link
                    href={`/bot/${coin.botId}`}
                    className="flex items-center"
                  >
                    <Bot className="w-4 h-4 mr-2" />
                    View Bot Details
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link
                    href={`/coin/${coin.token}`}
                    className="flex items-center"
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    View Coin Details
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-muted-foreground">
                  <div className="flex flex-col gap-1 text-xs">
                    <div>Entry: {formatPrice(coin.entryPrice)}</div>
                    <div>Amount: {formatAmount(coin.amount, coin.token)}</div>
                    <div>
                      Since:{" "}
                      {coin.timestamp.toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </div>
                  </div>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        ))}
      </SidebarMenu>

      {/* Token Details Modal */}
      {selectedToken && (
        <TokenDetailsView
          token={selectedToken}
          isOpen={showTokenDetails}
          onClose={() => {
            setShowTokenDetails(false);
            setSelectedToken(null);
          }}
          defaultTab="portfolio" // Open portfolio section by default
        />
      )}
    </SidebarGroup>
  );
}
