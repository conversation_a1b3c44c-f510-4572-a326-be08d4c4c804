/**
 * Real trading engine that executes trades based on technical indicators
 */

import {
  TradingSignalGenerator,
  TradingSignal,
  PricePoint,
} from "./trading-indicators";
import { realTimeDataService, PriceUpdate } from "./real-time-data";
import { Bot, Trade } from "./utils";

export interface TradingPosition {
  id: string;
  botId: string;
  symbol: string;
  type: "LONG" | "SHORT";
  entryPrice: number;
  amount: number;
  timestamp: number;
  stopLoss?: number;
  takeProfit?: number;
  currentPrice?: number;
  unrealizedPnL?: number;
}

export interface TradingEngineConfig {
  maxPositions: number;
  riskPerTrade: number; // Percentage of portfolio
  stopLossPercentage: number;
  takeProfitPercentage: number;
  minConfidence: number; // Minimum signal confidence to trade
}

export class TradingEngine {
  private bot: Bot;
  private config: TradingEngineConfig;
  private signalGenerator: TradingSignalGenerator;
  private positions: Map<string, TradingPosition> = new Map();
  private priceHistory: Map<string, PricePoint[]> = new Map();
  private isRunning: boolean = false;
  private subscribers: Set<(trade: Trade) => void> = new Set();

  constructor(bot: Bot, config?: Partial<TradingEngineConfig>) {
    this.bot = bot;
    this.config = {
      maxPositions: bot.config.maxPositions || 3,
      riskPerTrade: 2, // 2% risk per trade
      stopLossPercentage: bot.config.stopLoss || 5,
      takeProfitPercentage: bot.config.takeProfit || 15,
      minConfidence: 60,
      ...config,
    };

    // Initialize signal generator with bot-specific parameters
    const emaCrossFast = bot.config.emaCrossThreshold?.fastPeriod || 12;
    const emaCrossSlow = bot.config.emaCrossThreshold?.slowPeriod || 26;

    this.signalGenerator = new TradingSignalGenerator(
      14, // RSI period
      12, // MACD fast
      26, // MACD slow
      9, // MACD signal
      20, // MA period
      emaCrossFast,
      emaCrossSlow,
    );
  }

  start(): void {
    if (this.isRunning) return;

    this.isRunning = true;
    console.log(`🤖 Trading engine started for bot: ${this.bot.name}`);

    // Subscribe to price updates for monitoring
    this.subscribeToMarketData();

    // Start the main trading loop
    this.startTradingLoop();
  }

  stop(): void {
    this.isRunning = false;
    console.log(`🛑 Trading engine stopped for bot: ${this.bot.name}`);
  }

  private subscribeToMarketData(): void {
    // Subscribe to price updates for all relevant symbols
    // For demo purposes, we'll monitor popular Solana tokens
    const symbols = ["sol", "bonk", "ray", "jup", "wif"];

    symbols.forEach((symbol) => {
      realTimeDataService.subscribeToPrice(symbol, (update: PriceUpdate) => {
        this.handlePriceUpdate(symbol, update);
      });
    });
  }

  private handlePriceUpdate(symbol: string, update: PriceUpdate): void {
    // Update price history
    const history = this.priceHistory.get(symbol) || [];
    history.push({
      timestamp: update.timestamp,
      price: update.price,
      volume: update.volume_24h,
    });

    // Keep only last 100 data points for performance
    if (history.length > 100) {
      history.shift();
    }

    this.priceHistory.set(symbol, history);

    // Update existing positions
    this.updatePositions(symbol, update.price);

    // Check for new trading opportunities
    if (this.shouldAnalyzeSymbol(symbol)) {
      this.analyzeAndTrade(symbol, history);
    }
  }

  private shouldAnalyzeSymbol(symbol: string): boolean {
    // Only analyze if we have sufficient price history
    const history = this.priceHistory.get(symbol);
    return history ? history.length >= 30 : false;
  }

  private async analyzeAndTrade(
    symbol: string,
    priceHistory: PricePoint[],
  ): Promise<void> {
    try {
      // Generate trading signal with bot's strategy
      const signal = this.signalGenerator.generateSignal(
        priceHistory,
        this.bot.config.strategy,
      );

      if (!signal || signal.confidence < this.config.minConfidence) {
        return;
      }

      const currentPrice = priceHistory[priceHistory.length - 1].price;

      // Check if we should execute the trade
      if (signal.action === "BUY" && this.canOpenPosition()) {
        await this.openPosition(symbol, "LONG", currentPrice, signal);
      } else if (signal.action === "SELL") {
        await this.closePositions(symbol, currentPrice);
      }
    } catch (error) {
      console.error(`Trading analysis error for ${symbol}:`, error);
    }
  }

  private canOpenPosition(): boolean {
    return this.positions.size < this.config.maxPositions;
  }

  private async openPosition(
    symbol: string,
    type: "LONG" | "SHORT",
    price: number,
    signal: TradingSignal,
  ): Promise<void> {
    const positionId = `${this.bot.id}-${symbol}-${Date.now()}`;

    // Calculate position size based on risk management
    const portfolioValue = this.bot.solPerTrade * 100; // Assume 100x leverage for calculation
    const riskAmount = portfolioValue * (this.config.riskPerTrade / 100);
    const stopLossPrice =
      type === "LONG"
        ? price * (1 - this.config.stopLossPercentage / 100)
        : price * (1 + this.config.stopLossPercentage / 100);

    const riskPerUnit = Math.abs(price - stopLossPrice);
    const positionSize = riskAmount / riskPerUnit;

    const position: TradingPosition = {
      id: positionId,
      botId: this.bot.id,
      symbol,
      type,
      entryPrice: price,
      amount: positionSize,
      timestamp: Date.now(),
      stopLoss: stopLossPrice,
      takeProfit:
        type === "LONG"
          ? price * (1 + this.config.takeProfitPercentage / 100)
          : price * (1 - this.config.takeProfitPercentage / 100),
      currentPrice: price,
      unrealizedPnL: 0,
    };

    this.positions.set(positionId, position);

    // Create trade record
    const trade: Trade = {
      id: positionId,
      botId: this.bot.id,
      type: "BUY",
      token: symbol.toUpperCase(),
      amount: positionSize,
      price,
      timestamp: new Date(),
      status: "COMPLETED",
    };

    // Notify subscribers
    this.notifyTradeExecuted(trade);

    console.log(
      `📈 Opened ${type} position for ${symbol} at $${price} (Confidence: ${signal.confidence}%)`,
    );
  }

  private async closePositions(
    symbol: string,
    currentPrice: number,
  ): Promise<void> {
    const symbolPositions = Array.from(this.positions.values()).filter(
      (pos) => pos.symbol === symbol,
    );

    for (const position of symbolPositions) {
      await this.closePosition(position, currentPrice, "SIGNAL");
    }
  }

  private async closePosition(
    position: TradingPosition,
    exitPrice: number,
    reason: "SIGNAL" | "STOP_LOSS" | "TAKE_PROFIT",
  ): Promise<void> {
    // Calculate P&L
    const pnlMultiplier = position.type === "LONG" ? 1 : -1;
    const pnlPerUnit = (exitPrice - position.entryPrice) * pnlMultiplier;
    const totalPnL = pnlPerUnit * position.amount;
    const pnlPercentage = (pnlPerUnit / position.entryPrice) * 100;

    // Create exit trade record
    const trade: Trade = {
      id: `${position.id}-exit`,
      botId: this.bot.id,
      type: "SELL",
      token: position.symbol.toUpperCase(),
      amount: position.amount,
      price: exitPrice,
      pnl: totalPnL,
      timestamp: new Date(),
      status: "COMPLETED",
    };

    // Remove position
    this.positions.delete(position.id);

    // Notify subscribers
    this.notifyTradeExecuted(trade);

    console.log(
      `📉 Closed ${position.type} position for ${
        position.symbol
      } at $${exitPrice} (P&L: ${pnlPercentage.toFixed(
        2,
      )}%, Reason: ${reason})`,
    );
  }

  private updatePositions(symbol: string, currentPrice: number): void {
    const symbolPositions = Array.from(this.positions.values()).filter(
      (pos) => pos.symbol === symbol,
    );

    for (const position of symbolPositions) {
      position.currentPrice = currentPrice;

      // Calculate unrealized P&L
      const pnlMultiplier = position.type === "LONG" ? 1 : -1;
      const pnlPerUnit = (currentPrice - position.entryPrice) * pnlMultiplier;
      position.unrealizedPnL = pnlPerUnit * position.amount;

      // Check stop loss and take profit
      if (
        position.stopLoss &&
        this.shouldTriggerStopLoss(position, currentPrice)
      ) {
        this.closePosition(position, currentPrice, "STOP_LOSS");
      } else if (
        position.takeProfit &&
        this.shouldTriggerTakeProfit(position, currentPrice)
      ) {
        this.closePosition(position, currentPrice, "TAKE_PROFIT");
      }
    }
  }

  private shouldTriggerStopLoss(
    position: TradingPosition,
    currentPrice: number,
  ): boolean {
    if (!position.stopLoss) return false;

    return position.type === "LONG"
      ? currentPrice <= position.stopLoss
      : currentPrice >= position.stopLoss;
  }

  private shouldTriggerTakeProfit(
    position: TradingPosition,
    currentPrice: number,
  ): boolean {
    if (!position.takeProfit) return false;

    return position.type === "LONG"
      ? currentPrice >= position.takeProfit
      : currentPrice <= position.takeProfit;
  }

  private startTradingLoop(): void {
    // Main trading loop - runs every 30 seconds
    const loop = () => {
      if (!this.isRunning) return;

      // Perform periodic checks and maintenance
      this.performMaintenanceTasks();

      // Schedule next iteration
      setTimeout(loop, 30000); // 30 seconds
    };

    loop();
  }

  private performMaintenanceTasks(): void {
    // Clean up old price history
    this.priceHistory.forEach((history, symbol) => {
      if (history.length > 100) {
        this.priceHistory.set(symbol, history.slice(-100));
      }
    });

    // Log current status
    if (this.positions.size > 0) {
      console.log(
        `🔄 Bot ${this.bot.name}: ${this.positions.size} active positions`,
      );
    }
  }

  // Public methods for external access
  getPositions(): TradingPosition[] {
    return Array.from(this.positions.values());
  }

  getPositionCount(): number {
    return this.positions.size;
  }

  getTotalUnrealizedPnL(): number {
    return Array.from(this.positions.values()).reduce(
      (total, pos) => total + (pos.unrealizedPnL || 0),
      0,
    );
  }

  onTradeExecuted(callback: (trade: Trade) => void): () => void {
    this.subscribers.add(callback);
    return () => this.subscribers.delete(callback);
  }

  private notifyTradeExecuted(trade: Trade): void {
    this.subscribers.forEach((callback) => {
      try {
        callback(trade);
      } catch (error) {
        console.error("Trade notification error:", error);
      }
    });
  }

  // Manual trading methods
  async manualBuy(symbol: string): Promise<void> {
    const history = this.priceHistory.get(symbol);
    if (!history || history.length === 0) {
      throw new Error(`No price data available for ${symbol}`);
    }

    const currentPrice = history[history.length - 1].price;
    const signal: TradingSignal = {
      action: "BUY",
      confidence: 100, // Manual trade
      indicators: {},
      timestamp: Date.now(),
      price: currentPrice,
    };

    await this.openPosition(symbol, "LONG", currentPrice, signal);
  }

  async manualSell(symbol: string): Promise<void> {
    const history = this.priceHistory.get(symbol);
    if (!history || history.length === 0) {
      throw new Error(`No price data available for ${symbol}`);
    }

    const currentPrice = history[history.length - 1].price;

    await this.closePositions(symbol, currentPrice);
  }
}
