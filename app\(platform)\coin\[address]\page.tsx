"use client";

import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Image from "next/image";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbLink,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";

import { Button } from "@/components/ui/button";
import {
  TrendingUp,
  TrendingDown,
  Copy,
  ExternalLink,
  Bot,
  ArrowLeft,
} from "lucide-react";
import { NewCoinListing, realTimeDataService } from "@/lib/real-time-data";
import { useNewCoinListings } from "@/hooks/use-real-time-data";
import { BotSetupModal } from "@/components/bot-setup-modal";
import { TradingViewChart } from "@/components/trading-view-chart";
import { Dex<PERSON><PERSON><PERSON><PERSON>oinList } from "@/components/dexscreener-coin-list";

// Utility functions for formatting
const formatPrice = (price: number) => {
  if (price < 0.01) {
    return `$${price.toFixed(6)}`;
  } else if (price < 1) {
    return `$${price.toFixed(4)}`;
  } else {
    return `$${price.toFixed(2)}`;
  }
};

const formatNumber = (num: number) => {
  if (num >= 1e9) return `${(num / 1e9).toFixed(2)}B`;
  if (num >= 1e6) return `${(num / 1e6).toFixed(2)}M`;
  if (num >= 1e3) return `${(num / 1e3).toFixed(2)}K`;
  return num.toFixed(2);
};



export default function CoinDetailPage() {
  const params = useParams();
  const router = useRouter();
  const address = params.address as string;

  const [coin, setCoin] = useState<NewCoinListing | null>(null);
  const [botModalOpen, setBotModalOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  // Fetch coin listings to find the specific coin
  const { listings } = useNewCoinListings("solana", "all", 100);

  useEffect(() => {
    const fetchCoinData = async () => {
      if (!address) {
        setLoading(false);
        return;
      }

      // First try to find in current listings
      if (listings.length > 0) {
        const foundCoin = listings.find((c) => c.address === address);
        if (foundCoin) {
          setCoin(foundCoin);
          setLoading(false);
          return;
        }
      }

      // If not found in listings, try DexScreener
      try {
        console.log(`🔍 Fetching coin data from DexScreener for ${address}`);
        const dexCoin =
          await realTimeDataService.getCoinFromDexScreener(address);
        if (dexCoin) {
          setCoin(dexCoin);
          console.log(`✅ Found coin on DexScreener: ${dexCoin.name}`);
        } else {
          setCoin(null);
          console.log(`❌ Coin not found on DexScreener: ${address}`);
        }
      } catch (error) {
        console.error("Error fetching from DexScreener:", error);
        setCoin(null);
      }

      setLoading(false);
    };

    fetchCoinData();
  }, [listings, address]);

  const handleCopyAddress = async (address: string) => {
    try {
      await navigator.clipboard.writeText(address);
      console.log("Address copied to clipboard:", address);
    } catch (error) {
      console.error("Failed to copy address:", error);
    }
  };

  const handleSetBot = () => {
    if (coin) {
      setBotModalOpen(true);
    }
  };

  if (loading) {
    return (
      <div>
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 justify-between pr-6">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/coins">COINS</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>LOADING...</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        <div className="h-px bg-border" />
        <div className="space-y-4 p-6">
          <div className="text-center text-muted-foreground">
            Loading coin details...
          </div>
        </div>
      </div>
    );
  }

  if (!coin) {
    return (
      <div>
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 justify-between pr-6">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/coins">COINS</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>COIN NOT FOUND</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        <div className="h-px bg-border" />
        <div className="space-y-4 p-6">
          <div className="text-center">
            <div className="text-muted-foreground mb-4">
              Coin not found or no longer available
            </div>
            <Button onClick={() => router.push("/coins")} variant="outline">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to COINS
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Top Navigation Bar */}
      <header className="flex h-16 items-center justify-between px-6 border-b border-gray-800">
        <div className="flex items-center gap-4">
          <Button
            onClick={() => router.push("/coins")}
            variant="ghost"
            size="sm"
            className="text-gray-400 hover:text-white"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div className="h-6 w-px bg-gray-700" />
          <div className="flex items-center gap-3">
            {coin.image_url ? (
              <Image
                src={coin.image_url}
                alt={coin.name}
                width={32}
                height={32}
                className="rounded-full"
                onError={(e) => {
                  e.currentTarget.style.display = "none";
                }}
              />
            ) : (
              <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-bold">
                {coin.symbol.charAt(0)}
              </div>
            )}
            <div>
              <h1 className="text-xl font-bold">{coin.name}</h1>
              <p className="text-sm text-gray-400">{coin.symbol}</p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleCopyAddress(coin.address)}
            className="border-gray-700 text-gray-300 hover:bg-gray-800"
          >
            <Copy className="w-4 h-4 mr-2" />
            Copy CA
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open(coin.url, "_blank")}
            className="border-gray-700 text-gray-300 hover:bg-gray-800"
          >
            <ExternalLink className="w-4 h-4" />
          </Button>
          <Button
            onClick={handleSetBot}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Bot className="w-4 h-4 mr-2" />
            Trade
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex h-[calc(100vh-4rem)]">
        {/* Left Column - Chart and Price Info */}
        <div className="flex-1 flex flex-col">
          {/* Price Header */}
          <div className="p-6 border-b border-gray-800">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-6">
                <div>
                  <div className="text-3xl font-bold">
                    {formatPrice(coin.current_price)}
                  </div>
                  <div className="text-sm text-gray-400">USD</div>
                </div>
                <div
                  className={`flex items-center gap-2 px-3 py-1 rounded-lg ${
                    coin.price_change_24h >= 0
                      ? "bg-green-900/30 text-green-400"
                      : "bg-red-900/30 text-red-400"
                  }`}
                >
                  {coin.price_change_24h >= 0 ? (
                    <TrendingUp className="h-4 w-4" />
                  ) : (
                    <TrendingDown className="h-4 w-4" />
                  )}
                  <span className="font-semibold">
                    {coin.price_change_24h.toFixed(2)}%
                  </span>
                  <span className="text-xs">24h</span>
                </div>
              </div>

              <div className="flex items-center gap-4 text-sm">
                <div>
                  <div className="text-gray-400">Volume 24h</div>
                  <div className="font-semibold">
                    ${formatNumber(coin.volume_24h)}
                  </div>
                </div>
                <div>
                  <div className="text-gray-400">Market Cap</div>
                  <div className="font-semibold">
                    ${formatNumber(coin.market_cap)}
                  </div>
                </div>
                <div>
                  <div className="text-gray-400">Liquidity</div>
                  <div className="font-semibold">
                    ${formatNumber(coin.liquidity)}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Chart Section */}
          <div className="flex-1 p-6">
            <div className="bg-gray-900/50 rounded-lg border border-gray-800 h-full">
              <div className="p-4 border-b border-gray-800">
                <h2 className="text-lg font-semibold">Price Chart</h2>
              </div>
              <div className="p-4 h-[calc(100%-4rem)]">
                <TradingViewChart symbol={coin.symbol} />
              </div>
            </div>
          </div>
        </div>

        {/* Right Sidebar - Trending Tokens */}
        <div className="w-80 border-l border-gray-800 bg-gray-950/50">
          <div className="p-4 border-b border-gray-800">
            <h2 className="text-lg font-semibold">Trending Tokens</h2>
          </div>
          <div className="p-4">
            <DexScreenerCoinList
              chainId="solana"
              limit={10}
              showHeader={false}
            />
          </div>
        </div>
      </div>

      {/* Bot Setup Modal */}
      <BotSetupModal
        isOpen={botModalOpen}
        onClose={() => setBotModalOpen(false)}
        coin={coin}
      />
    </div>
  );
}
