import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types based on the existing Convex schema
export interface UserProfile {
  id: string
  user_id: string
  email: string
  created_at: string
  updated_at: string
}

export interface NumberRecord {
  id: string
  value: number
  created_at: string
}

// Database operations to replace Convex functions
export const supabaseOperations = {
  // User profile operations
  async upsertUserProfile(userId: string, email: string): Promise<string> {
    const { data, error } = await supabase
      .from('user_profiles')
      .upsert(
        {
          user_id: userId,
          email: email,
          updated_at: new Date().toISOString(),
        },
        {
          onConflict: 'user_id',
        }
      )
      .select('id')
      .single()

    if (error) {
      throw new Error(`Failed to upsert user profile: ${error.message}`)
    }

    return data.id
  },

  async getUserProfile(userId: string): Promise<UserProfile | null> {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows returned
        return null
      }
      throw new Error(`Failed to get user profile: ${error.message}`)
    }

    return data
  },

  // Number operations (from original Convex schema)
  async addNumber(value: number): Promise<string> {
    const { data, error } = await supabase
      .from('numbers')
      .insert({ value })
      .select('id')
      .single()

    if (error) {
      throw new Error(`Failed to add number: ${error.message}`)
    }

    return data.id
  },

  async listNumbers(count: number): Promise<{ numbers: number[] }> {
    const { data, error } = await supabase
      .from('numbers')
      .select('value')
      .order('created_at', { ascending: false })
      .limit(count)

    if (error) {
      throw new Error(`Failed to list numbers: ${error.message}`)
    }

    return {
      numbers: data.map(item => item.value).reverse()
    }
  }
}
