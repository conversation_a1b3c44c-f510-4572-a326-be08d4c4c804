"use client";

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ArrowUpDown, ChevronDown } from "lucide-react";
import { useRouter } from "next/navigation";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { Badge } from "@/components/ui/badge";
import {
  Breadcrumb,
  BreadcrumbItem,
  B<PERSON>crumbList,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { RefreshCw, TrendingUp, TrendingDown, Bot, Copy } from "lucide-react";
import { useNewCoinListings } from "@/hooks/use-real-time-data";
import { NewCoinListing } from "@/lib/real-time-data";
import { BotSetupModal } from "@/components/bot-setup-modal";
import {
  SimpleFiltersComponent,
  SimpleFilters,
} from "@/components/simple-filters";
import Image from "next/image";

// Utility functions for formatting
const formatPrice = (price: number) => {
  if (price < 0.01) {
    return `$${price.toFixed(6)}`;
  } else if (price < 1) {
    return `$${price.toFixed(4)}`;
  } else {
    return `$${price.toFixed(2)}`;
  }
};

const formatNumber = (num: number) => {
  if (num >= 1e9) return `${(num / 1e9).toFixed(2)}B`;
  if (num >= 1e6) return `${(num / 1e6).toFixed(2)}M`;
  if (num >= 1e3) return `${(num / 1e3).toFixed(2)}K`;
  return num.toFixed(2);
};

const formatAge = (ageHours: number) => {
  if (ageHours < 1) {
    return `${Math.floor(ageHours * 60)}m`;
  } else if (ageHours < 24) {
    return `${Math.floor(ageHours)}h`;
  } else {
    return `${Math.floor(ageHours / 24)}d`;
  }
};

const getAgeColor = (ageHours: number) => {
  if (ageHours < 1) return "text-green-500";
  if (ageHours < 6) return "text-yellow-500";
  return "text-gray-500";
};

export default function TrendingPage() {
  const router = useRouter();
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  const [botModalOpen, setBotModalOpen] = React.useState(false);
  const [selectedCoin, setSelectedCoin] = React.useState<NewCoinListing | null>(
    null,
  );
  const [filters, setFilters] = React.useState<SimpleFilters>({});

  // Fetch new coin listings for Solana only
  const {
    listings: newListings,
    loading,
    error,
    forceRefresh,
  } = useNewCoinListings("solana", "all", 50);

  // Apply filters
  const data = React.useMemo(() => {
    let filtered = newListings;

    // Apply filters
    if (filters.minPrice !== undefined) {
      filtered = filtered.filter(
        (coin) => coin.current_price >= filters.minPrice!,
      );
    }
    if (filters.maxPrice !== undefined) {
      filtered = filtered.filter(
        (coin) => coin.current_price <= filters.maxPrice!,
      );
    }
    if (filters.minVolume !== undefined) {
      filtered = filtered.filter(
        (coin) => coin.volume_24h >= filters.minVolume!,
      );
    }
    if (filters.maxAge !== undefined) {
      filtered = filtered.filter((coin) => coin.age_hours <= filters.maxAge!);
    }
    if (filters.dexId) {
      filtered = filtered.filter((coin) => coin.dex_id === filters.dexId);
    }

    // Sort by age (newest first)
    return filtered.sort((a, b) => a.age_hours - b.age_hours);
  }, [newListings, filters]);

  const handleSetBot = (coin: NewCoinListing) => {
    setSelectedCoin(coin);
    setBotModalOpen(true);
  };

  const handleCopyAddress = async (address: string) => {
    try {
      await navigator.clipboard.writeText(address);
      // You can add a toast notification here if you have a toast system
      console.log("Address copied to clipboard:", address);
    } catch (error) {
      console.error("Failed to copy address:", error);
    }
  };

  const handleCoinClick = (coin: NewCoinListing) => {
    router.push(`/coin/${coin.address}`);
  };

  const columns: ColumnDef<NewCoinListing>[] = [
    {
      id: "copy",
      header: "Copy",
      cell: ({ row }) => {
        const coin = row.original;
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleCopyAddress(coin.address)}
            className="h-8 w-8 p-0">
            <Copy className="h-4 w-4" />
            <span className="sr-only">Copy token address</span>
          </Button>
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: "Token",
      cell: ({ row }) => {
        const coin = row.original;
        return (
          <div
            className="flex items-center gap-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 p-2 rounded-lg transition-colors"
            onClick={() => handleCoinClick(coin)}>
            <div className="relative">
              {coin.image_url ? (
                <Image
                  src={coin.image_url}
                  alt={coin.name}
                  width={32}
                  height={32}
                  className="rounded-full border border-gray-200 dark:border-gray-700"
                  onError={(e) => {
                    e.currentTarget.style.display = "none";
                  }}
                />
              ) : (
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-bold">
                  {coin.symbol.charAt(0)}
                </div>
              )}
            </div>
            <div className="min-w-0 flex-1">
              <div className="font-semibold text-gray-900 dark:text-white truncate">
                {coin.symbol}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400 truncate max-w-[200px]">
                {coin.name}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "current_price",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() =>
              column.toggleSorting(column.getIsSorted() === "asc")
            }>
            Price
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const price = parseFloat(row.getValue("current_price"));
        return <div className="font-medium">{formatPrice(price)}</div>;
      },
    },
    {
      accessorKey: "price_change_24h",
      header: "24h Change",
      cell: ({ row }) => {
        const change = parseFloat(row.getValue("price_change_24h"));
        return (
          <div
            className={`flex items-center gap-1 ${
              change >= 0 ? "text-green-500" : "text-red-500"
            }`}>
            {change >= 0 ? (
              <TrendingUp className="h-4 w-4" />
            ) : (
              <TrendingDown className="h-4 w-4" />
            )}
            {change.toFixed(2)}%
          </div>
        );
      },
    },
    {
      accessorKey: "volume_24h",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() =>
              column.toggleSorting(column.getIsSorted() === "asc")
            }>
            Volume 24h
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const volume = parseFloat(row.getValue("volume_24h"));
        return <div>${formatNumber(volume)}</div>;
      },
    },
    {
      accessorKey: "market_cap",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() =>
              column.toggleSorting(column.getIsSorted() === "asc")
            }>
            Market Cap
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const marketCap = parseFloat(row.getValue("market_cap"));
        return <div>${formatNumber(marketCap)}</div>;
      },
    },
    {
      accessorKey: "age_hours",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() =>
              column.toggleSorting(column.getIsSorted() === "asc")
            }>
            Age
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const age = parseFloat(row.getValue("age_hours"));
        return (
          <Badge variant="outline" className={getAgeColor(age)}>
            {formatAge(age)}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        const age = parseFloat(row.getValue(id));
        if (value === "new") return age <= 1; // Less than 1 hour
        if (value === "recent") return age <= 24; // Less than 24 hours
        if (value === "old") return age > 24; // More than 24 hours
        return true;
      },
    },

    {
      id: "start_bot",
      header: "Bot",
      enableHiding: false,
      cell: ({ row }) => {
        const coin = row.original;
        return (
          <Button
            size="sm"
            onClick={() => handleSetBot(coin)}
            className="flex items-center gap-1">
            <Bot className="h-3 w-3" />
            Start Bot
          </Button>
        );
      },
    },
  ];

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div>
      <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 justify-between pr-6">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbPage>TRENCH</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
        <div className="text-sm text-muted-foreground">
          2,434,342 COINS LISTED
        </div>
      </header>
      <div className="h-px bg-border" />

      <div className="space-y-4 p-6">
        {/* Table Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Input
              placeholder="Filter tokens..."
              value={
                (table.getColumn("name")?.getFilterValue() as string) ?? ""
              }
              onChange={(event) =>
                table.getColumn("name")?.setFilterValue(event.target.value)
              }
              className="max-w-sm"
            />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  Solana <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>Solana</DropdownMenuItem>
                <DropdownMenuItem>Ethereum</DropdownMenuItem>
                <DropdownMenuItem>Bitcoin</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  Pumpswap <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>Pumpswap</DropdownMenuItem>
                <DropdownMenuItem>Pumpfun</DropdownMenuItem>
                <DropdownMenuItem>Radium</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  Age Filter <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={() =>
                    table.getColumn("age_hours")?.setFilterValue("")
                  }>
                  All Ages
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() =>
                    table.getColumn("age_hours")?.setFilterValue("new")
                  }>
                  New (&lt; 1h)
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() =>
                    table.getColumn("age_hours")?.setFilterValue("recent")
                  }>
                  Recent (&lt; 24h)
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() =>
                    table.getColumn("age_hours")?.setFilterValue("old")
                  }>
                  Old (&gt; 24h)
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  Columns <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {table
                  .getAllColumns()
                  .filter((column) => column.getCanHide())
                  .map((column) => {
                    return (
                      <DropdownMenuCheckboxItem
                        key={column.id}
                        className="capitalize"
                        checked={column.getIsVisible()}
                        onCheckedChange={(value) =>
                          column.toggleVisibility(!!value)
                        }>
                        {column.id}
                      </DropdownMenuCheckboxItem>
                    );
                  })}
              </DropdownMenuContent>
            </DropdownMenu>
            <SimpleFiltersComponent
              filters={filters}
              onFiltersChange={setFilters}
              chain="solana"
            />
            <Button
              variant="outline"
              size="sm"
              onClick={forceRefresh}
              disabled={loading}
              className="flex items-center gap-2">
              <RefreshCw
                className={`h-4 w-4 ${loading ? "animate-spin" : ""}`}
              />
              Force Refresh
            </Button>
          </div>
          {error && <div className="text-sm text-red-600">Error: {error}</div>}
        </div>

        {/* Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center">
                    {loading ? "Loading tokens..." : "No tokens found."}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-end space-x-2 py-4">
          <div className="flex-1 text-sm text-muted-foreground">
            {table.getFilteredSelectedRowModel().rows.length} of{" "}
            {table.getFilteredRowModel().rows.length} row(s) selected.
          </div>
          <div className="space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}>
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}>
              Next
            </Button>
          </div>
        </div>
      </div>

      {/* Bot Setup Modal */}
      <BotSetupModal
        isOpen={botModalOpen}
        onClose={() => {
          setBotModalOpen(false);
          setSelectedCoin(null);
        }}
        coin={selectedCoin}
      />
    </div>
  );
}
