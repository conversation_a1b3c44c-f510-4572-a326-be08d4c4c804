"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import {
  Zap,
  TrendingUp,
  Target,
  Clock,
  Brain,
  Coins,
  AlertCircle,
  Info,
  Activity,
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import { useBots } from "@/contexts/bot-context";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { CreateBotData, TradingMode } from "@/types/bot";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@radix-ui/react-separator";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb";
// Link import removed - not used in this component

const strategyOptions = [
  {
    id: "RSI" as const,
    name: "RSI Strategy",
    description: "Trade based on RSI overbought/oversold levels",
    icon: TrendingUp,
    color: "bg-blue-500/20 text-blue-400 border-blue-500/30",
  },
  {
    id: "MOMENTUM" as const,
    name: "Momentum Trading",
    description: "Follow price momentum and volume spikes",
    icon: Zap,
    color: "bg-green-500/20 text-green-400 border-green-500/30",
  },
  // {
  //   id: 'AI_TREND' as const,
  //   name: 'AI Trend Analysis',
  //   description: 'Machine learning trend prediction',
  //   icon: Bot,
  //   color: 'bg-purple-500/20 text-purple-400 border-purple-500/30'
  // },
  {
    id: "SCALPING" as const,
    name: "Scalping",
    description: "High-frequency short-term trades",
    icon: Target,
    color: "bg-orange-500/20 text-orange-400 border-orange-500/30",
  },
  {
    id: "EMA_CROSS" as const,
    name: "EMA Cross",
    description: "Trade on EMA crossover signals (fast/slow)",
    icon: Activity,
    color: "bg-cyan-500/20 text-cyan-400 border-cyan-500/30",
  },
  {
    id: "CUSTOM" as const,
    name: "Custom Strategy",
    description: "Define your own trading parameters",
    icon: Clock,
    color: "bg-gray-500/20 text-gray-400 border-gray-500/30",
  },
];

interface ValidationErrors {
  name?: string;
  description?: string;
  solPerTrade?: string;
  aiPrompt?: string;
  selectedCoin?: string;
  fastPeriod?: string;
  slowPeriod?: string;
}

export default function CreateBotPage() {
  const router = useRouter();
  const { createCustomBot, bots } = useBots();
  const [step, setStep] = useState(1);
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>(
    {},
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<CreateBotData>({
    name: "",
    description: "",
    tradingMode: "AI_TRADING",
    strategy: "AI_PROMPT",
    solPerTrade: 0.1,
    config: {
      stopLoss: 5,
      takeProfit: 15,
      maxSlippage: 2,
      minLiquidity: 10,
      strategy: "AI_PROMPT",
      timeframe: "5m",
      maxPositions: 3,
    },
    aiPrompt: "",
    selectedCoin: "",
  });

  // Function to generate bot name automatically
  const generateBotName = (
    tradingMode: TradingMode,
    strategy: string,
    selectedCoin?: string,
  ) => {
    const botCount = bots.length + 1;
    const modePrefix = tradingMode === "AI_TRADING" ? "AI" : "Coin";

    if (tradingMode === "COIN_TRADING" && selectedCoin) {
      // Use first 8 characters of token address for brevity
      const tokenShort = selectedCoin.substring(0, 8);
      return `${modePrefix} ${tokenShort} ${strategy} Bot ${botCount}`;
    }

    return `${modePrefix} ${strategy} Bot ${botCount}`;
  };

  // Function to generate bot description automatically
  const generateBotDescription = (
    tradingMode: TradingMode,
    strategy: string,
    selectedCoin?: string,
    aiPrompt?: string,
  ) => {
    if (tradingMode === "AI_TRADING") {
      return aiPrompt
        ? `AI-powered ${strategy.toLowerCase()} trading bot with custom instructions`
        : `AI-powered ${strategy.toLowerCase()} trading bot`;
    } else {
      return selectedCoin
        ? `${strategy} strategy bot for token ${selectedCoin.substring(
            0,
            8,
          )}...`
        : `${strategy} strategy trading bot`;
    }
  };

  const handleStrategySelect = (strategy: CreateBotData["strategy"]) => {
    const defaultConfigs = {
      RSI: {
        stopLoss: 5,
        takeProfit: 15,
        maxSlippage: 2,
        minLiquidity: 10,
        rsiThreshold: { buy: 30, sell: 70 },
        strategy: "RSI" as const,
        timeframe: "5m" as const,
        maxPositions: 3,
      },
      MOMENTUM: {
        stopLoss: 8,
        takeProfit: 25,
        maxSlippage: 3,
        minLiquidity: 20,
        strategy: "MOMENTUM" as const,
        timeframe: "1m" as const,
        maxPositions: 5,
      },
      // AI_TREND: {
      //   stopLoss: 10,
      //   takeProfit: 20,
      //   maxSlippage: 1.5,
      //   minLiquidity: 50,
      //   strategy: 'AI_TREND' as const,
      //   timeframe: '15m' as const,
      //   maxPositions: 2
      // },
      SCALPING: {
        stopLoss: 2,
        takeProfit: 5,
        maxSlippage: 0.5,
        minLiquidity: 5,
        strategy: "SCALPING" as const,
        timeframe: "1m" as const,
        maxPositions: 10,
      },
      CUSTOM: {
        stopLoss: 5,
        takeProfit: 15,
        maxSlippage: 2,
        minLiquidity: 10,
        strategy: "CUSTOM" as const,
        timeframe: "5m" as const,
        maxPositions: 3,
      },
      AI_PROMPT: {
        stopLoss: 5,
        takeProfit: 15,
        maxSlippage: 2,
        minLiquidity: 10,
        strategy: "AI_PROMPT" as const,
        timeframe: "5m" as const,
        maxPositions: 3,
      },
      EMA_CROSS: {
        stopLoss: 3,
        takeProfit: 10,
        maxSlippage: 1.5,
        minLiquidity: 15,
        emaCrossThreshold: { fastPeriod: 12, slowPeriod: 26 },
        strategy: "EMA_CROSS" as const,
        timeframe: "5m" as const,
        maxPositions: 4,
      },
    };

    setFormData((prev) => ({
      ...prev,
      strategy,
      config: defaultConfigs[strategy],
    }));
  };

  const handleSubmit = () => {
    setIsSubmitting(true);
    const errors: ValidationErrors = {};

    // Validate SOL per trade
    if (formData.solPerTrade <= 0) {
      errors.solPerTrade = "SOL per trade must be greater than 0";
    }

    // Validate AI Trading specific fields
    if (formData.tradingMode === "AI_TRADING") {
      if (!formData.aiPrompt?.trim()) {
        errors.aiPrompt = "Please enter an AI trading prompt";
      }
    }

    // Validate Coin Trading specific fields
    if (formData.tradingMode === "COIN_TRADING") {
      if (!formData.selectedCoin?.trim()) {
        errors.selectedCoin = "Please enter a target coin token address";
      }
    }

    // Validate EMA Cross specific fields
    if (
      formData.strategy === "EMA_CROSS" &&
      formData.config.emaCrossThreshold
    ) {
      const { fastPeriod, slowPeriod } = formData.config.emaCrossThreshold;
      if (fastPeriod < 5 || fastPeriod > 50) {
        errors.fastPeriod = "Fast EMA period must be between 5 and 50";
      }
      if (slowPeriod < 10 || slowPeriod > 100) {
        errors.slowPeriod = "Slow EMA period must be between 10 and 100";
      }
      if (fastPeriod >= slowPeriod) {
        errors.fastPeriod = "Fast EMA period must be less than slow EMA period";
      }
    }

    // If there are validation errors, show them and return
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      setIsSubmitting(false);
      // Show first error as toast
      const firstError = Object.values(errors)[0];
      toast.error(firstError);
      return;
    }

    // Clear any previous errors
    setValidationErrors({});

    // Generate name and description automatically
    const generatedName = generateBotName(
      formData.tradingMode,
      formData.strategy,
      formData.selectedCoin,
    );
    const generatedDescription = generateBotDescription(
      formData.tradingMode,
      formData.strategy,
      formData.selectedCoin,
      formData.aiPrompt,
    );

    const botDataWithGeneratedFields = {
      ...formData,
      name: generatedName,
      description: generatedDescription,
    };

    try {
      createCustomBot(botDataWithGeneratedFields);
      toast.success(`${generatedName} created successfully!`);

      // Redirect to bot dashboard to show the new bot in the sidebar
      router.push("/bot");
    } catch (error) {
      console.error("Error creating bot:", error);
      toast.error("Failed to create bot. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-background ">
      <header className=" flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 justify-between pr-6 border-b ">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbPage>Create New Bot</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
        {/* Progress Steps */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="flex items-center justify-end p-6"
        >
          {[1, 2, 3].map((stepNum) => (
            <div key={stepNum} className="flex items-center">
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center text-sm text-white font-medium ${
                  step >= stepNum
                    ? "bg-neutral-700 text-primary-foreground"
                    : "bg-muted-gradient text-muted-foreground"
                }`}
              >
                {stepNum}
              </div>
              {stepNum < 3 && (
                <div
                  className={`w-16 h-0.5 mx-2 ${
                    step > stepNum ? "bg-neutral-700" : "bg-muted-gradient"
                  }`}
                />
              )}
            </div>
          ))}
        </motion.div>
        {/* <Link href="/create-bot">
          <Button className="bg-primary-gradient text-white ">
            Create New Bot
          </Button>
        </Link> */}
      </header>
      <div className="container mx-auto px-4 py-12 max-w-4xl ">
        {/* Header */}
        {/* <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8">
          <div className="text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-4 flex items-center justify-center gap-3">
              <Bot className="h-8 w-8 text-primary" />
              Create Trading Bot
            </h1>
            <p className="text-muted-foreground text-lg">
              Build your custom trading bot with advanced strategies and
              parameters
            </p>
          </div>
        </motion.div> */}

        {/* Form Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="bg-card border-border">
            <CardContent className="p-8">
              {/* Step 1: Trading Mode Selection */}
              {step === 1 && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="space-y-6"
                >
                  <div className="text-center mb-8">
                    <h2 className="text-2xl font-bold text-foreground mb-2">
                      Choose Trading Mode
                    </h2>
                    <p className="text-muted-foreground">
                      Select how you want your bot to trade
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* AI Trading Option */}
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() =>
                        setFormData((prev) => ({
                          ...prev,
                          tradingMode: "AI_TRADING",
                          strategy: "AI_PROMPT",
                        }))
                      }
                      className={`p-6 rounded-lg border cursor-pointer transition-all ${
                        formData.tradingMode === "AI_TRADING"
                          ? "border-primary bg-primary/10"
                          : "border-border hover:border-primary/50"
                      }`}
                    >
                      <div className="flex flex-col items-center text-center space-y-4">
                        <div className="p-4 rounded-lg bg-purple-500/20 text-purple-400 border-purple-500/30">
                          <Brain className="h-8 w-8" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-foreground mb-2">
                            AI Trading
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            Give AI a prompt on how you want it to select coins
                            and trade. The AI will analyze market conditions and
                            execute trades based on your instructions.
                          </p>
                        </div>
                        {formData.tradingMode === "AI_TRADING" && (
                          <div className="w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                            <div className="w-2 h-2 bg-primary-foreground rounded-full" />
                          </div>
                        )}
                      </div>
                    </motion.div>

                    {/* Coin Trading Option */}
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() =>
                        setFormData((prev) => ({
                          ...prev,
                          tradingMode: "COIN_TRADING",
                          strategy: "RSI",
                        }))
                      }
                      className={`p-6 rounded-lg border cursor-pointer transition-all ${
                        formData.tradingMode === "COIN_TRADING"
                          ? "border-primary bg-primary/10"
                          : "border-border hover:border-primary/50"
                      }`}
                    >
                      <div className="flex flex-col items-center text-center space-y-4">
                        <div className="p-4 rounded-lg bg-blue-500/20 text-blue-400 border-blue-500/30">
                          <Coins className="h-8 w-8" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-foreground mb-2">
                            Coin Trading
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            Select a particular coin and set buy/sell conditions
                            based on technical indicators like RSI, momentum, or
                            custom parameters.
                          </p>
                        </div>
                        {formData.tradingMode === "COIN_TRADING" && (
                          <div className="w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                            <div className="w-2 h-2 bg-primary-foreground rounded-full" />
                          </div>
                        )}
                      </div>
                    </motion.div>
                  </div>

                  <div className="flex justify-end pt-6">
                    <Button
                      onClick={() => setStep(2)}
                      className="bg-primary-gradient hover:bg-primary-gradient/90 text-white"
                    >
                      Next Step
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 2: Configuration Based on Trading Mode */}
              {step === 2 && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="space-y-6"
                >
                  {/* AI Trading Configuration */}
                  {formData.tradingMode === "AI_TRADING" && (
                    <>
                      <div className="text-center mb-8">
                        <h2 className="text-2xl font-bold text-foreground mb-2">
                          AI Trading Setup
                        </h2>
                        <p className="text-muted-foreground">
                          Configure your bot details and AI trading prompt
                        </p>
                      </div>

                      <div>
                        <label className="text-sm text-muted-foreground mb-2 block">
                          AI Trading Prompt
                        </label>
                        <textarea
                          value={formData.aiPrompt || ""}
                          onChange={(e) =>
                            setFormData((prev) => ({
                              ...prev,
                              aiPrompt: e.target.value,
                            }))
                          }
                          placeholder="Describe how you want the AI to trade. For example: 'Focus on meme coins with high volume and social media buzz. Buy when RSI is below 30 and sell when it reaches 70. Avoid coins with less than $100k market cap.'"
                          rows={4}
                          className={`w-full px-3 py-2 bg-input border border-border text-foreground rounded-md text-sm resize-none ${
                            validationErrors.aiPrompt ? "border-red-500" : ""
                          }`}
                        />
                        {validationErrors.aiPrompt && (
                          <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                            <AlertCircle className="h-3 w-3" />
                            {validationErrors.aiPrompt}
                          </p>
                        )}
                      </div>

                      <div>
                        <label className="text-sm text-muted-foreground mb-2 block">
                          Base Trading Strategy
                        </label>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {strategyOptions.map((strategy) => (
                            <motion.div
                              key={strategy.id}
                              whileHover={{ scale: 1.02 }}
                              whileTap={{ scale: 0.98 }}
                              onClick={() => handleStrategySelect(strategy.id)}
                              className={`p-4 rounded-lg border cursor-pointer transition-all ${
                                formData.strategy === strategy.id
                                  ? "border-primary bg-primary/10"
                                  : "border-border hover:border-primary/50"
                              }`}
                            >
                              <div className="flex items-start gap-3">
                                <div
                                  className={`p-2 rounded-lg ${strategy.color}`}
                                >
                                  <strategy.icon className="h-4 w-4" />
                                </div>
                                <div className="flex-1">
                                  <div className="font-medium text-foreground text-sm mb-1">
                                    {strategy.name}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {strategy.description}
                                  </div>
                                </div>
                                {formData.strategy === strategy.id && (
                                  <div className="w-4 h-4 bg-primary rounded-full flex items-center justify-center">
                                    <div className="w-1.5 h-1.5 bg-primary-foreground rounded-full" />
                                  </div>
                                )}
                              </div>
                            </motion.div>
                          ))}
                        </div>
                        <p className="text-xs text-muted-foreground mt-2">
                          The AI will use this as a base strategy and enhance it
                          with your custom prompt instructions.
                        </p>
                      </div>
                    </>
                  )}

                  {/* Coin Trading Configuration */}
                  {formData.tradingMode === "COIN_TRADING" && (
                    <>
                      <div className="text-center mb-8">
                        <h2 className="text-2xl font-bold text-foreground mb-2">
                          Coin Trading Setup
                        </h2>
                        <p className="text-muted-foreground">
                          Configure your bot details and select trading strategy
                        </p>
                      </div>

                      <div>
                        <label className="text-sm text-muted-foreground mb-2 block">
                          Target Coin Token Address
                        </label>
                        <Input
                          value={formData.selectedCoin || ""}
                          onChange={(e) =>
                            setFormData((prev) => ({
                              ...prev,
                              selectedCoin: e.target.value,
                            }))
                          }
                          placeholder="Enter coin token address (e.g., DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263)"
                          className={`bg-input border-border text-foreground ${
                            validationErrors.selectedCoin
                              ? "border-red-500"
                              : ""
                          }`}
                        />
                        {validationErrors.selectedCoin ? (
                          <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                            <AlertCircle className="h-3 w-3" />
                            {validationErrors.selectedCoin}
                          </p>
                        ) : (
                          <p className="text-xs text-muted-foreground mt-1">
                            Enter the Solana token address of the coin you want
                            to trade
                          </p>
                        )}
                      </div>

                      <div>
                        <label className="text-sm text-muted-foreground mb-2 block">
                          Trading Strategy
                        </label>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {strategyOptions.map((strategy) => (
                            <motion.div
                              key={strategy.id}
                              whileHover={{ scale: 1.02 }}
                              whileTap={{ scale: 0.98 }}
                              onClick={() => handleStrategySelect(strategy.id)}
                              className={`p-4 rounded-lg border cursor-pointer transition-all ${
                                formData.strategy === strategy.id
                                  ? "border-primary bg-primary/10"
                                  : "border-border hover:border-primary/50"
                              }`}
                            >
                              <div className="flex items-start gap-3">
                                <div
                                  className={`p-2 rounded-lg ${strategy.color}`}
                                >
                                  <strategy.icon className="h-4 w-4" />
                                </div>
                                <div className="flex-1">
                                  <div className="font-medium text-foreground text-sm mb-1">
                                    {strategy.name}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {strategy.description}
                                  </div>
                                </div>
                                {formData.strategy === strategy.id && (
                                  <div className="w-4 h-4 bg-primary rounded-full flex items-center justify-center">
                                    <div className="w-1.5 h-1.5 bg-primary-foreground rounded-full" />
                                  </div>
                                )}
                              </div>
                            </motion.div>
                          ))}
                        </div>
                      </div>
                    </>
                  )}

                  <div className="flex justify-between pt-6">
                    <Button variant="outline" onClick={() => setStep(1)}>
                      Previous
                    </Button>
                    <Button
                      onClick={() => setStep(3)}
                      className="bg-primary-gradient text-primary hover:bg-primary-foreground/90"
                    >
                      Next Step
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 3: Configuration */}
              {step === 3 && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="space-y-6"
                >
                  <div className="text-center mb-8">
                    <h2 className="text-2xl font-bold text-foreground mb-2">
                      Configuration
                    </h2>
                    <p className="text-muted-foreground">
                      Fine-tune your bot&apos;s trading parameters
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                      <label className="text-sm text-muted-foreground mb-2 block">
                        SOL per Trade
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0.01"
                        value={formData.solPerTrade}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            solPerTrade: parseFloat(e.target.value) || 0,
                          }))
                        }
                        className={`bg-input border-border text-foreground ${
                          validationErrors.solPerTrade ? "border-red-500" : ""
                        }`}
                      />
                      {validationErrors.solPerTrade && (
                        <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" />
                          {validationErrors.solPerTrade}
                        </p>
                      )}
                    </div>

                    <div>
                      <label className="text-sm text-muted-foreground mb-2 block">
                        Max Positions
                      </label>
                      <Input
                        type="number"
                        min="1"
                        value={formData.config.maxPositions}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            config: {
                              ...prev.config,
                              maxPositions: parseInt(e.target.value) || 1,
                            },
                          }))
                        }
                        className="bg-input border-border text-foreground"
                      />
                    </div>

                    <div>
                      <label className="text-sm text-muted-foreground mb-2 block">
                        Min Liquidity (SOL)
                      </label>
                      <Input
                        type="number"
                        step="1"
                        value={formData.config.minLiquidity}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            config: {
                              ...prev.config,
                              minLiquidity: parseFloat(e.target.value) || 0,
                            },
                          }))
                        }
                        className="bg-input border-border text-foreground"
                      />
                    </div>

                    <div>
                      <label className="text-sm text-muted-foreground mb-2 block">
                        Stop Loss (%)
                      </label>
                      <Input
                        type="number"
                        step="0.1"
                        value={formData.config.stopLoss}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            config: {
                              ...prev.config,
                              stopLoss: parseFloat(e.target.value) || 0,
                            },
                          }))
                        }
                        className="bg-input border-border text-foreground"
                      />
                    </div>

                    <div>
                      <label className="text-sm text-muted-foreground mb-2 block">
                        Take Profit (%)
                      </label>
                      <Input
                        type="number"
                        step="0.1"
                        value={formData.config.takeProfit}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            config: {
                              ...prev.config,
                              takeProfit: parseFloat(e.target.value) || 0,
                            },
                          }))
                        }
                        className="bg-input border-border text-foreground"
                      />
                    </div>

                    <div>
                      <label className="text-sm text-muted-foreground mb-2 block">
                        Max Slippage (%)
                      </label>
                      <Input
                        type="number"
                        step="0.1"
                        value={formData.config.maxSlippage}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            config: {
                              ...prev.config,
                              maxSlippage: parseFloat(e.target.value) || 0,
                            },
                          }))
                        }
                        className="bg-input border-border text-foreground"
                      />
                    </div>

                    {/* <div>
                      <label className="text-sm text-muted-foreground mb-2 block">
                        Timeframe
                      </label>
                      <select
                        value={formData.config.timeframe}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            config: {
                              ...prev.config,
                              timeframe: e.target.value as
                                | "1m"
                                | "5m"
                                | "15m"
                                | "1h",
                            },
                          }))
                        }
                        className="w-full h-9 px-3 py-1 bg-input border border-border text-foreground rounded-md text-sm">
                        <option value="1m">1 Minute</option>
                        <option value="5m">5 Minutes</option>
                        <option value="15m">15 Minutes</option>
                        <option value="1h">1 Hour</option>
                      </select>
                    </div> */}
                  </div>

                  {/* RSI Specific Settings */}
                  {formData.strategy === "RSI" &&
                    formData.config.rsiThreshold && (
                      <div className="space-y-4 pt-6 border-t border-border">
                        <h3 className="text-foreground font-medium">
                          RSI Settings
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label className="text-sm text-muted-foreground mb-2 block">
                              RSI Buy Threshold
                            </label>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              value={formData.config.rsiThreshold.buy}
                              onChange={(e) =>
                                setFormData((prev) => ({
                                  ...prev,
                                  config: {
                                    ...prev.config,
                                    rsiThreshold: {
                                      ...prev.config.rsiThreshold!,
                                      buy: parseInt(e.target.value) || 0,
                                    },
                                  },
                                }))
                              }
                              className="bg-input border-border text-foreground"
                            />
                          </div>

                          <div>
                            <label className="text-sm text-muted-foreground mb-2 block">
                              RSI Sell Threshold
                            </label>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              value={formData.config.rsiThreshold.sell}
                              onChange={(e) =>
                                setFormData((prev) => ({
                                  ...prev,
                                  config: {
                                    ...prev.config,
                                    rsiThreshold: {
                                      ...prev.config.rsiThreshold!,
                                      sell: parseInt(e.target.value) || 0,
                                    },
                                  },
                                }))
                              }
                              className="bg-input border-border text-foreground"
                            />
                          </div>
                        </div>
                      </div>
                    )}

                  {/* EMA Cross Specific Settings */}
                  {formData.strategy === "EMA_CROSS" &&
                    formData.config.emaCrossThreshold && (
                      <div className="space-y-4 pt-6 border-t border-border">
                        <h3 className="text-foreground font-medium">
                          EMA Cross Settings
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <label className="text-sm text-muted-foreground mb-2 block">
                              Fast EMA Period
                            </label>
                            <Input
                              type="number"
                              min="5"
                              max="50"
                              value={
                                formData.config.emaCrossThreshold.fastPeriod
                              }
                              onChange={(e) =>
                                setFormData((prev) => ({
                                  ...prev,
                                  config: {
                                    ...prev.config,
                                    emaCrossThreshold: {
                                      ...prev.config.emaCrossThreshold!,
                                      fastPeriod:
                                        parseInt(e.target.value) || 12,
                                    },
                                  },
                                }))
                              }
                              className={`bg-input border-border text-foreground ${
                                validationErrors.fastPeriod
                                  ? "border-red-500"
                                  : ""
                              }`}
                            />
                            {validationErrors.fastPeriod ? (
                              <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                                <AlertCircle className="h-3 w-3" />
                                {validationErrors.fastPeriod}
                              </p>
                            ) : (
                              <p className="text-xs text-muted-foreground mt-1">
                                Shorter period for faster signals (default: 12)
                              </p>
                            )}
                          </div>

                          <div>
                            <label className="text-sm text-muted-foreground mb-2 block">
                              Slow EMA Period
                            </label>
                            <Input
                              type="number"
                              min="10"
                              max="100"
                              value={
                                formData.config.emaCrossThreshold.slowPeriod
                              }
                              onChange={(e) =>
                                setFormData((prev) => ({
                                  ...prev,
                                  config: {
                                    ...prev.config,
                                    emaCrossThreshold: {
                                      ...prev.config.emaCrossThreshold!,
                                      slowPeriod:
                                        parseInt(e.target.value) || 26,
                                    },
                                  },
                                }))
                              }
                              className={`bg-input border-border text-foreground ${
                                validationErrors.slowPeriod
                                  ? "border-red-500"
                                  : ""
                              }`}
                            />
                            {validationErrors.slowPeriod ? (
                              <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                                <AlertCircle className="h-3 w-3" />
                                {validationErrors.slowPeriod}
                              </p>
                            ) : (
                              <p className="text-xs text-muted-foreground mt-1">
                                Longer period for trend confirmation (default:
                                26)
                              </p>
                            )}
                          </div>
                        </div>
                        <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                          <div className="flex items-start gap-3">
                            <Info className="h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0" />
                            <div className="text-sm">
                              <p className="text-blue-400 font-medium mb-1">
                                EMA Cross Strategy
                              </p>
                              <p className="text-blue-300/80">
                                Bot buys when fast EMA crosses above slow EMA
                                (bullish signal) and sells when fast EMA crosses
                                below slow EMA (bearish signal). Lower periods =
                                more sensitive to price changes.
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                  <div className="flex justify-between pt-6">
                    <Button variant="outline" onClick={() => setStep(2)}>
                      Previous
                    </Button>
                    <Button
                      onClick={handleSubmit}
                      disabled={isSubmitting}
                      className="bg-primary-gradient hover:bg-primary-gradient/90 text-white disabled:opacity-50"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Creating...
                        </>
                      ) : (
                        "Create Bot"
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
