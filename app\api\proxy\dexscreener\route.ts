import { NextRequest, NextResponse } from "next/server";
import { DexScreenerService } from "@/lib/dexscreener-service";

const dexScreenerService = new DexScreenerService();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get("q");
    const trending = searchParams.get("trending");

    // Handle trending tokens request
    if (trending === "true") {
      const chainId = searchParams.get("chain") || "solana";
      const tokens = await dexScreenerService.getTrendingTokens(chainId);

      return NextResponse.json({
        pairs: tokens,
        schemaVersion: "1.0.0",
      });
    }

    // Handle search request
    if (!query) {
      return NextResponse.json(
        { error: "Query parameter 'q' is required for search" },
        { status: 400 },
      );
    }

    // Search for tokens using DexScreener
    const tokens = await dexScreenerService.searchTokens(query);

    return NextResponse.json({
      pairs: tokens,
      schemaVersion: "1.0.0",
    });
  } catch (error) {
    console.error("Error proxying DexScreener request:", error);
    return NextResponse.json(
      { error: "Failed to fetch data from DexScreener" },
      { status: 500 },
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { addresses } = body;

    if (!addresses || !Array.isArray(addresses)) {
      return NextResponse.json(
        { error: "Addresses array is required" },
        { status: 400 },
      );
    }

    // Get pairs by addresses using DexScreener
    const tokens = await dexScreenerService.getPairsByAddresses(addresses);

    return NextResponse.json({
      pairs: tokens,
      schemaVersion: "1.0.0",
    });
  } catch (error) {
    console.error("Error proxying DexScreener request:", error);
    return NextResponse.json(
      { error: "Failed to fetch data from DexScreener" },
      { status: 500 },
    );
  }
}
