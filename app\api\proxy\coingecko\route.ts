import { NextRequest, NextResponse } from "next/server";

const COINGECKO_BASE_URL = "https://api.coingecko.com/api/v3";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const endpoint = searchParams.get("endpoint");
    const limit = searchParams.get("limit") || "50";
    const ids = searchParams.get("ids");
    const coinId = searchParams.get("coinId");
    const days = searchParams.get("days");
    const page = searchParams.get("page") || "1";
    const order = searchParams.get("order") || "market_cap_desc";

    if (!endpoint) {
      return NextResponse.json(
        { error: "Endpoint parameter is required" },
        { status: 400 },
      );
    }

    let url: string;

    // Handle different CoinGecko endpoints
    switch (endpoint) {
      case "markets":
        if (ids) {
          url = `${COINGECKO_BASE_URL}/coins/markets?vs_currency=usd&ids=${ids}&order=${order}&per_page=250&page=${page}&sparkline=false&price_change_percentage=24h`;
        } else {
          url = `${COINGECKO_BASE_URL}/coins/markets?vs_currency=usd&order=${order}&per_page=${limit}&page=${page}&sparkline=false&price_change_percentage=24h`;
        }
        break;
      case "trending":
        url = `${COINGECKO_BASE_URL}/search/trending`;
        break;
      case "new-listings":
        // Use markets endpoint with recent coins
        url = `${COINGECKO_BASE_URL}/coins/markets?vs_currency=usd&order=market_cap_desc&per_page=${limit}&page=1&sparkline=false&price_change_percentage=24h`;
        break;
      case "market_chart":
        if (!coinId || !days) {
          return NextResponse.json(
            {
              error:
                "coinId and days parameters are required for market_chart endpoint",
            },
            { status: 400 },
          );
        }
        url = `${COINGECKO_BASE_URL}/coins/${coinId}/market_chart?vs_currency=usd&days=${days}`;
        break;
      default:
        return NextResponse.json(
          { error: "Unsupported endpoint" },
          { status: 400 },
        );
    }

    console.log(`🦎 Fetching from CoinGecko: ${url}`);

    const response = await fetch(url, {
      headers: {
        Accept: "application/json",
        "User-Agent": "DexTrip/1.0",
      },
    });

    if (!response.ok) {
      console.error(
        `CoinGecko API error: ${response.status} ${response.statusText}`,
      );
      return NextResponse.json(
        { error: `CoinGecko API error: ${response.status}` },
        { status: response.status },
      );
    }

    const data = await response.json();
    console.log(
      `🦎 Successfully fetched ${Array.isArray(data) ? data.length : "data"} from CoinGecko`,
    );

    return NextResponse.json(data);
  } catch (error) {
    console.error("Error proxying CoinGecko request:", error);
    return NextResponse.json(
      { error: "Failed to fetch data from CoinGecko" },
      { status: 500 },
    );
  }
}
