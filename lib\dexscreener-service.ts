export interface StandardTokenFormat {
  id: string;
  symbol: string;
  name: string;
  address: string;
  current_price: number;
  price_change_24h: number;
  price_change_1h: number;
  price_change_5m: number;
  volume_24h: number;
  volume_1h: number;
  market_cap: number;
  liquidity: number;
  fdv: number;
  image: string;
  image_url: string;
  dex_id: string;
  pair_address: string;
  url: string;
  created_at: number;
  last_updated: string;
  chain: string;
  age_hours: number;
  holders: number;
  transactions_24h: number;
  buys_24h: number;
  sells_24h: number;
  price_change_percentage_24h: number;
}

export interface DexScreenerToken {
  chainId: string;
  dexId: string;
  url: string;
  pairAddress: string;
  baseToken: {
    address: string;
    name: string;
    symbol: string;
  };
  quoteToken: {
    address: string;
    name: string;
    symbol: string;
  };
  priceNative: string;
  priceUsd?: string;
  txns: {
    m5: {
      buys: number;
      sells: number;
    };
    h1: {
      buys: number;
      sells: number;
    };
    h6: {
      buys: number;
      sells: number;
    };
    h24: {
      buys: number;
      sells: number;
    };
  };
  volume: {
    h24: number;
    h6: number;
    h1: number;
    m5: number;
  };
  priceChange: {
    m5: number;
    h1: number;
    h6: number;
    h24: number;
  };
  liquidity?: {
    usd?: number;
    base: number;
    quote: number;
  };
  fdv?: number;
  marketCap?: number;
  pairCreatedAt?: number;
  info?: {
    imageUrl?: string;
    websites?: Array<{ label: string; url: string }>;
    socials?: Array<{ type: string; url: string }>;
  };
}

export interface DexScreenerSearchResult {
  schemaVersion: string;
  pairs: DexScreenerToken[];
}

export class DexScreenerService {
  private baseUrl = "https://api.dexscreener.com/latest";
  private rateLimitDelay = 300; // 300ms between requests to be safe

  /**
   * Search for tokens by query (name, symbol, or address)
   */
  async searchTokens(query: string): Promise<DexScreenerToken[]> {
    try {
      // Add rate limiting delay
      await this.delay(this.rateLimitDelay);

      const response = await fetch(
        `${this.baseUrl}/dex/search/?q=${encodeURIComponent(query)}`,
        {
          headers: {
            Accept: "application/json",
            "User-Agent": "DexTrip/1.0",
          },
        },
      );

      if (!response.ok) {
        throw new Error(`DexScreener API error: ${response.status}`);
      }

      const data: DexScreenerSearchResult = await response.json();
      return data.pairs || [];
    } catch (error) {
      console.error("Error searching DexScreener:", error);
      return [];
    }
  }

  /**
   * Get token info by address
   */
  async getTokenByAddress(address: string): Promise<DexScreenerToken[]> {
    try {
      await this.delay(this.rateLimitDelay);

      const response = await fetch(`${this.baseUrl}/dex/tokens/${address}`, {
        headers: {
          Accept: "application/json",
          "User-Agent": "DexTrip/1.0",
        },
      });

      if (!response.ok) {
        throw new Error(`DexScreener API error: ${response.status}`);
      }

      const data: DexScreenerSearchResult = await response.json();
      return data.pairs || [];
    } catch (error) {
      console.error("Error fetching token by address:", error);
      return [];
    }
  }

  /**
   * Get pairs by addresses
   */
  async getPairsByAddresses(addresses: string[]): Promise<DexScreenerToken[]> {
    try {
      await this.delay(this.rateLimitDelay);

      const addressList = addresses.join(",");
      const response = await fetch(`${this.baseUrl}/dex/pairs/${addressList}`, {
        headers: {
          Accept: "application/json",
          "User-Agent": "DexTrip/1.0",
        },
      });

      if (!response.ok) {
        throw new Error(`DexScreener API error: ${response.status}`);
      }

      const data: DexScreenerSearchResult = await response.json();
      return data.pairs || [];
    } catch (error) {
      console.error("Error fetching pairs by addresses:", error);
      return [];
    }
  }

  /**
   * Get trending tokens for a specific chain
   */
  async getTrendingTokens(
    chainId: string = "solana",
  ): Promise<DexScreenerToken[]> {
    try {
      await this.delay(this.rateLimitDelay);

      // DexScreener doesn't have a direct trending endpoint, so we'll search for popular tokens
      const popularQueries = [
        "BONK",
        "PEPE",
        "WIF",
        "BOME",
        "SLERF",
        "JUP",
        "RAY",
        "ORCA",
      ];
      const allResults: DexScreenerToken[] = [];
      const seenTokens = new Set<string>(); // Track seen token addresses

      for (const query of popularQueries) {
        const results = await this.searchTokens(query);
        const chainResults = results.filter(
          (token) => token.chainId === chainId,
        );

        // Add only unique tokens based on base token address
        for (const token of chainResults.slice(0, 1)) {
          // Take only top result per query
          const tokenKey = `${token.baseToken.address}-${token.chainId}`;
          if (!seenTokens.has(tokenKey)) {
            seenTokens.add(tokenKey);
            allResults.push(token);
          }
        }
      }

      // Sort by volume and return top results
      return allResults
        .sort((a, b) => (b.volume?.h24 || 0) - (a.volume?.h24 || 0))
        .slice(0, 10); // Limit to 10 unique tokens
    } catch (error) {
      console.error("Error fetching trending tokens:", error);
      return [];
    }
  }

  /**
   * Convert DexScreener token to our standard format
   */
  convertToStandardFormat(dexToken: DexScreenerToken): StandardTokenFormat {
    const baseToken = dexToken.baseToken;
    const priceUsd = parseFloat(dexToken.priceUsd || "0");
    const volume24h = dexToken.volume?.h24 || 0;
    const priceChange24h = dexToken.priceChange?.h24 || 0;
    const marketCap = dexToken.marketCap || 0;
    const liquidity = dexToken.liquidity?.usd || 0;

    return {
      id: baseToken.address,
      symbol: baseToken.symbol,
      name: baseToken.name,
      address: baseToken.address,
      current_price: priceUsd,
      price_change_24h: priceChange24h,
      price_change_1h: dexToken.priceChange?.h1 || 0,
      price_change_5m: dexToken.priceChange?.m5 || 0,
      volume_24h: volume24h,
      volume_1h: dexToken.volume?.h1 || 0,
      market_cap: marketCap,
      liquidity: liquidity,
      fdv: dexToken.fdv || marketCap,
      image: this.getTokenImageUrl(dexToken), // Use 'image' for consistency
      image_url: this.getTokenImageUrl(dexToken), // Keep both for compatibility
      dex_id: dexToken.dexId,
      pair_address: dexToken.pairAddress,
      url: dexToken.url,
      created_at: dexToken.pairCreatedAt || Date.now(),
      last_updated: new Date().toISOString(),
      chain: this.mapChainId(dexToken.chainId),
      age_hours: dexToken.pairCreatedAt
        ? (Date.now() - dexToken.pairCreatedAt) / (1000 * 60 * 60)
        : 0,
      holders: 0, // DexScreener doesn't provide holder count
      transactions_24h:
        (dexToken.txns?.h24?.buys || 0) + (dexToken.txns?.h24?.sells || 0),
      buys_24h: dexToken.txns?.h24?.buys || 0,
      sells_24h: dexToken.txns?.h24?.sells || 0,
      price_change_percentage_24h: priceChange24h,
    };
  }

  /**
   * Get token image URL with fallbacks
   */
  private getTokenImageUrl(dexToken: DexScreenerToken): string {
    // Try DexScreener image first
    if (dexToken.info?.imageUrl) {
      return dexToken.info.imageUrl;
    }

    // Fallback to common token images based on symbol
    const symbol = dexToken.baseToken.symbol.toUpperCase();
    const commonTokenImages: Record<string, string> = {
      BONK: "https://assets.coingecko.com/coins/images/28600/small/bonk.jpg",
      PEPE: "https://assets.coingecko.com/coins/images/29850/small/pepe-token.jpeg",
      WIF: "https://assets.coingecko.com/coins/images/33767/small/dogwifhat.jpg",
      JUP: "https://assets.coingecko.com/coins/images/33835/small/jup.png",
      RAY: "https://assets.coingecko.com/coins/images/13928/small/PSigc4ie_400x400.jpg",
      ORCA: "https://assets.coingecko.com/coins/images/17547/small/Orca_Logo.png",
      SOL: "https://assets.coingecko.com/coins/images/4128/small/solana.png",
      USDC: "https://assets.coingecko.com/coins/images/6319/small/USD_Coin_icon.png",
      USDT: "https://assets.coingecko.com/coins/images/325/small/Tether.png",
      ETH: "https://assets.coingecko.com/coins/images/279/small/ethereum.png",
      BTC: "https://assets.coingecko.com/coins/images/1/small/bitcoin.png",
    };

    if (commonTokenImages[symbol]) {
      return commonTokenImages[symbol];
    }

    // Generate a placeholder image URL based on token symbol
    return `https://via.placeholder.com/40x40/6366f1/ffffff?text=${symbol.charAt(
      0,
    )}`;
  }

  /**
   * Map DexScreener chain IDs to our standard format
   */
  private mapChainId(chainId: string): string {
    const chainMap: Record<string, string> = {
      ethereum: "ethereum",
      bsc: "ethereum", // Map BSC to ethereum for compatibility
      polygon: "ethereum",
      solana: "solana",
      arbitrum: "ethereum",
      optimism: "ethereum",
      avalanche: "ethereum",
      fantom: "ethereum",
    };

    return chainMap[chainId] || "ethereum";
  }

  /**
   * Rate limiting delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Check if a query looks like a token address
   */
  isTokenAddress(query: string): boolean {
    // Ethereum address (42 chars, starts with 0x)
    if (/^0x[a-fA-F0-9]{40}$/.test(query)) return true;

    // Solana address (32-44 chars, base58)
    if (/^[1-9A-HJ-NP-Za-km-z]{32,44}$/.test(query)) return true;

    return false;
  }

  /**
   * Enhanced search that tries both search and direct address lookup
   */
  async enhancedSearch(query: string): Promise<DexScreenerToken[]> {
    const results: DexScreenerToken[] = [];
    const seenTokens = new Set<string>();

    try {
      // If it looks like an address, try direct lookup first
      if (this.isTokenAddress(query)) {
        const addressResults = await this.getTokenByAddress(query);
        for (const token of addressResults) {
          const tokenKey = `${token.baseToken.address}-${token.chainId}`;
          if (!seenTokens.has(tokenKey)) {
            seenTokens.add(tokenKey);
            results.push(token);
          }
        }
      }

      // Always try search as well (unless we already found exact address match)
      if (results.length === 0 || !this.isTokenAddress(query)) {
        const searchResults = await this.searchTokens(query);
        for (const token of searchResults) {
          const tokenKey = `${token.baseToken.address}-${token.chainId}`;
          if (!seenTokens.has(tokenKey)) {
            seenTokens.add(tokenKey);
            results.push(token);
          }
        }
      }

      // Sort by volume (most liquid first) and limit results
      return results
        .sort((a, b) => (b.volume?.h24 || 0) - (a.volume?.h24 || 0))
        .slice(0, 15); // Limit to 15 results to prevent overwhelming UI
    } catch (error) {
      console.error("Error in enhanced search:", error);
      return [];
    }
  }
}
