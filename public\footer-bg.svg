
<svg width="1920" height="1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient Definitions -->
    <linearGradient id="mainGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0ca0d0;stop-opacity:0.8" />
      <stop offset="25%" style="stop-color:#4349e1;stop-opacity:0.6" />
      <stop offset="50%" style="stop-color:#6934c9;stop-opacity:0.4" />
      <stop offset="75%" style="stop-color:#db2f83;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#f3661b;stop-opacity:0.8" />
    </linearGradient>
    
    <radialGradient id="radial1" cx="20%" cy="30%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:0" />
    </radialGradient>
    
    <radialGradient id="radial2" cx="80%" cy="70%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:0.4" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:0" />
    </radialGradient>
    
    <radialGradient id="radial3" cx="50%" cy="50%">
      <stop offset="0%" style="stop-color:#ec4899;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#ec4899;stop-opacity:0" />
    </radialGradient>
    
    <!-- Pattern Definition -->
    <pattern id="grid" width="100" height="100" patternUnits="userSpaceOnUse">
      <path d="M 100 0 L 0 0 0 100" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/>
    </pattern>
  </defs>
  
  <!-- Base Background -->
  <rect width="100%" height="100%" fill="url(#mainGrad)"/>
  
  <!-- Radial Overlays -->
  <circle cx="384" cy="324" r="400" fill="url(#radial1)"/>
  <circle cx="1536" cy="756" r="500" fill="url(#radial2)"/>
  <circle cx="960" cy="540" r="300" fill="url(#radial3)"/>
  
  <!-- Grid Pattern -->
  <rect width="100%" height="100%" fill="url(#grid)"/>
  
  <!-- Floating Elements -->
  <g opacity="0.1">
    <!-- Trading Chart Lines -->
    <path d="M100,800 Q300,600 500,700 T900,500 T1300,600 T1700,400" 
          fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
    <path d="M200,900 Q400,700 600,800 T1000,600 T1400,700 T1800,500" 
          fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
    
    <!-- Geometric Shapes -->
    <polygon points="300,200 350,150 400,200 350,250" fill="rgba(255,255,255,0.1)"/>
    <polygon points="1200,300 1250,250 1300,300 1250,350" fill="rgba(255,255,255,0.1)"/>
    <polygon points="800,800 850,750 900,800 850,850" fill="rgba(255,255,255,0.1)"/>
    
    <!-- Circles -->
    <circle cx="150" cy="150" r="30" fill="rgba(255,255,255,0.08)"/>
    <circle cx="1650" cy="200" r="40" fill="rgba(255,255,255,0.08)"/>
    <circle cx="1400" cy="900" r="35" fill="rgba(255,255,255,0.08)"/>
  </g>
  
  <!-- Subtle Text Watermark -->
  <g opacity="0.03">
    <text x="960" y="540" text-anchor="middle" font-family="Arial, sans-serif" 
          font-size="120" font-weight="bold" fill="white">DEXTRIP</text>
  </g>
</svg>
