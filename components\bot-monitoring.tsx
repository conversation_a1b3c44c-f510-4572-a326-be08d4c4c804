"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Activity, AlertCircle, CheckCircle, Bell, X } from "lucide-react";
import { <PERSON>, <PERSON>Content, <PERSON>H<PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useBots } from "@/contexts/bot-context";

interface BotAlert {
  id: string;
  botId: string;
  botName: string;
  type: "success" | "warning" | "error" | "info";
  message: string;
  timestamp: Date;
  read: boolean;
}

export function BotMonitoring() {
  const { bots } = useBots();
  const [alerts, setAlerts] = useState<BotAlert[]>([]);
  const [showNotifications, setShowNotifications] = useState(false);

  // Simulate real-time monitoring
  useEffect(() => {
    const interval = setInterval(() => {
      const activeBots = bots.filter((bot) => bot.isActive);

      activeBots.forEach((bot) => {
        // Simulate random events
        const eventChance = Math.random();

        if (eventChance > 0.95) {
          // 5% chance of generating an alert
          const alertTypes = [
            {
              type: "success" as const,
              messages: [
                "Profitable trade executed",
                "Target profit reached",
                "Stop loss avoided",
              ],
            },
            {
              type: "warning" as const,
              messages: [
                "High volatility detected",
                "Low liquidity warning",
                "Unusual market conditions",
              ],
            },
            {
              type: "error" as const,
              messages: [
                "Trade execution failed",
                "Connection timeout",
                "Insufficient balance",
              ],
            },
            {
              type: "info" as const,
              messages: [
                "Market analysis complete",
                "Position updated",
                "Strategy parameters adjusted",
              ],
            },
          ];

          const randomType =
            alertTypes[Math.floor(Math.random() * alertTypes.length)];
          const randomMessage =
            randomType.messages[
              Math.floor(Math.random() * randomType.messages.length)
            ];

          const newAlert: BotAlert = {
            id: `alert-${Date.now()}-${Math.random()}`,
            botId: bot.id,
            botName: bot.name,
            type: randomType.type,
            message: randomMessage,
            timestamp: new Date(),
            read: false,
          };

          setAlerts((prev) => [newAlert, ...prev].slice(0, 50)); // Keep last 50 alerts
        }
      });
    }, 3000); // Check every 3 seconds

    return () => clearInterval(interval);
  }, [bots]);

  const activeBots = bots.filter((bot) => bot.isActive);
  const unreadAlerts = alerts.filter((alert) => !alert.read);

  const markAsRead = (alertId: string) => {
    setAlerts((prev) =>
      prev.map((alert) =>
        alert.id === alertId ? { ...alert, read: true } : alert,
      ),
    );
  };

  const clearAllAlerts = () => {
    setAlerts([]);
  };

  const getAlertIcon = (type: BotAlert["type"]) => {
    switch (type) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "warning":
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case "info":
        return <Activity className="h-4 w-4 text-blue-500" />;
    }
  };

  const getAlertColor = (type: BotAlert["type"]) => {
    switch (type) {
      case "success":
        return "border-green-500/20 bg-green-500/5";
      case "warning":
        return "border-yellow-500/20 bg-yellow-500/5";
      case "error":
        return "border-red-500/20 bg-red-500/5";
      case "info":
        return "border-blue-500/20 bg-blue-500/5";
    }
  };

  return (
    <div className="space-y-6">
      {/* Monitoring Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Bot Monitoring</h2>
          <p className="text-muted-foreground">
            Real-time status and alerts for your trading bots
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowNotifications(!showNotifications)}
            className="relative"
          >
            <Bell className="h-4 w-4 mr-2" />
            Alerts
            {unreadAlerts.length > 0 && (
              <Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs">
                {unreadAlerts.length}
              </Badge>
            )}
          </Button>
        </div>
      </div>

      {/* Active Bots Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {activeBots.length > 0 ? (
          activeBots.map((bot, index) => (
            <motion.div
              key={bot.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="relative overflow-hidden">
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-500 to-blue-500" />
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-sm font-medium">
                      {bot.name}
                    </CardTitle>
                    <div className="flex items-center gap-1">
                      <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                      <span className="text-xs text-green-500">Active</span>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Total Trades</span>
                    <span className="font-medium">{bot.stats.totalTrades}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Win Rate</span>
                    <span className="font-medium">
                      {bot.stats.winRate.toFixed(1)}%
                    </span>
                  </div>
                  <Progress value={bot.stats.winRate} className="h-2" />
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">P&L</span>
                    <span
                      className={`font-medium ${bot.stats.pnl >= 0 ? "text-green-500" : "text-red-500"}`}
                    >
                      {bot.stats.pnl >= 0 ? "+" : ""}
                      {bot.stats.pnl.toFixed(4)} SOL
                    </span>
                  </div>
                  {bot.stats.currentPosition && (
                    <div className="pt-2 border-t">
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-muted-foreground">
                          Current Position
                        </span>
                        <Badge variant="outline" className="text-xs">
                          {bot.stats.currentPosition.token}
                        </Badge>
                      </div>
                      <div className="flex justify-between text-xs mt-1">
                        <span>P&L:</span>
                        <span
                          className={
                            bot.stats.currentPosition.pnl >= 0
                              ? "text-green-500"
                              : "text-red-500"
                          }
                        >
                          {bot.stats.currentPosition.pnl >= 0 ? "+" : ""}
                          {bot.stats.currentPosition.pnl.toFixed(4)} SOL
                        </span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          ))
        ) : (
          <div className="col-span-full text-center py-8">
            <Activity className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No Active Bots</h3>
            <p className="text-muted-foreground">
              Start a bot to begin monitoring
            </p>
          </div>
        )}
      </div>

      {/* Notifications Panel */}
      <AnimatePresence>
        {showNotifications && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="overflow-hidden"
          >
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="h-5 w-5" />
                    Recent Alerts
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    {alerts.length > 0 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={clearAllAlerts}
                      >
                        Clear All
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowNotifications(false)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {alerts.length > 0 ? (
                    alerts.slice(0, 10).map((alert) => (
                      <motion.div
                        key={alert.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        className={`p-3 rounded-lg border cursor-pointer transition-colors hover:bg-muted/50 ${getAlertColor(
                          alert.type,
                        )} ${!alert.read ? "border-l-4" : ""}`}
                        onClick={() => markAsRead(alert.id)}
                      >
                        <div className="flex items-start gap-3">
                          {getAlertIcon(alert.type)}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="text-sm font-medium">
                                {alert.botName}
                              </span>
                              {!alert.read && (
                                <div className="h-2 w-2 bg-blue-500 rounded-full" />
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground">
                              {alert.message}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {alert.timestamp.toLocaleTimeString()}
                            </p>
                          </div>
                        </div>
                      </motion.div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p>No alerts yet</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
