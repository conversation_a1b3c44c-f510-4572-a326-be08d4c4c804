export interface CoinGeckoToken {
  id: string;
  symbol: string;
  name: string;
  image: string;
  current_price: number;
  market_cap: number;
  market_cap_rank: number;
  fully_diluted_valuation: number;
  total_volume: number;
  high_24h: number;
  low_24h: number;
  price_change_24h: number;
  price_change_percentage_24h: number;
  market_cap_change_24h: number;
  market_cap_change_percentage_24h: number;
  circulating_supply: number;
  total_supply: number;
  max_supply: number;
  ath: number;
  ath_change_percentage: number;
  ath_date: string;
  atl: number;
  atl_change_percentage: number;
  atl_date: string;
  roi: unknown;
  last_updated: string;
}

export interface CoinGeckoSearchResult {
  coins: Array<{
    id: string;
    name: string;
    symbol: string;
    market_cap_rank: number;
    thumb: string;
    large: string;
  }>;
  exchanges: Array<unknown>;
  icos: Array<unknown>;
  categories: Array<unknown>;
  nfts: Array<unknown>;
}

export interface CoinGeckoTrendingResult {
  coins: Array<{
    item: {
      id: string;
      coin_id: number;
      name: string;
      symbol: string;
      market_cap_rank: number;
      thumb: string;
      small: string;
      large: string;
      slug: string;
      price_btc: number;
      score: number;
    };
  }>;
}

export class CoinGeckoService {
  private baseUrl = "https://api.coingecko.com/api/v3";
  private proBaseUrl = "https://pro-api.coingecko.com/api/v3";
  private apiKey?: string;

  constructor(apiKey?: string) {
    this.apiKey = apiKey;
  }

  private getHeaders() {
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    if (this.apiKey) {
      headers["x-cg-pro-api-key"] = this.apiKey;
    }

    return headers;
  }

  private getBaseUrl() {
    return this.apiKey ? this.proBaseUrl : this.baseUrl;
  }

  /**
   * Search for coins by query
   */
  async searchCoins(query: string): Promise<CoinGeckoSearchResult> {
    try {
      const response = await fetch(
        `${this.getBaseUrl()}/search?query=${encodeURIComponent(query)}`,
        {
          headers: this.getHeaders(),
        },
      );

      if (!response.ok) {
        throw new Error(`CoinGecko API error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error searching coins:", error);
      throw error;
    }
  }

  /**
   * Get trending coins
   */
  async getTrendingCoins(): Promise<CoinGeckoTrendingResult> {
    try {
      const response = await fetch(`${this.getBaseUrl()}/search/trending`, {
        headers: this.getHeaders(),
      });

      if (!response.ok) {
        throw new Error(`CoinGecko API error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error fetching trending coins:", error);
      throw error;
    }
  }

  /**
   * Get coins market data
   */
  async getCoinsMarket(
    coinIds: string[],
    vsCurrency = "usd",
    options: {
      order?: string;
      perPage?: number;
      page?: number;
      sparkline?: boolean;
      priceChangePercentage?: string;
    } = {},
  ): Promise<CoinGeckoToken[]> {
    try {
      const params = new URLSearchParams({
        ids: coinIds.join(","),
        vs_currency: vsCurrency,
        order: options.order || "market_cap_desc",
        per_page: (options.perPage || 100).toString(),
        page: (options.page || 1).toString(),
        sparkline: (options.sparkline || false).toString(),
      });

      if (options.priceChangePercentage) {
        params.append("price_change_percentage", options.priceChangePercentage);
      }

      const response = await fetch(
        `${this.getBaseUrl()}/coins/markets?${params}`,
        {
          headers: this.getHeaders(),
        },
      );

      if (!response.ok) {
        throw new Error(`CoinGecko API error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error fetching coins market data:", error);
      throw error;
    }
  }

  /**
   * Get detailed coin data by ID
   */
  async getCoinById(
    coinId: string,
    options: {
      localization?: boolean;
      tickers?: boolean;
      marketData?: boolean;
      communityData?: boolean;
      developerData?: boolean;
      sparkline?: boolean;
    } = {},
  ) {
    try {
      const params = new URLSearchParams({
        localization: (options.localization || false).toString(),
        tickers: (options.tickers || false).toString(),
        market_data: (options.marketData || true).toString(),
        community_data: (options.communityData || false).toString(),
        developer_data: (options.developerData || false).toString(),
        sparkline: (options.sparkline || false).toString(),
      });

      const response = await fetch(
        `${this.getBaseUrl()}/coins/${coinId}?${params}`,
        {
          headers: this.getHeaders(),
        },
      );

      if (!response.ok) {
        throw new Error(`CoinGecko API error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error fetching coin details:", error);
      throw error;
    }
  }

  /**
   * Get top coins by market cap
   */
  async getTopCoins(limit = 50, vsCurrency = "usd"): Promise<CoinGeckoToken[]> {
    try {
      const response = await fetch(
        `${this.getBaseUrl()}/coins/markets?vs_currency=${vsCurrency}&order=market_cap_desc&per_page=${limit}&page=1&sparkline=false&price_change_percentage=24h`,
        {
          headers: this.getHeaders(),
        },
      );

      if (!response.ok) {
        throw new Error(`CoinGecko API error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error fetching top coins:", error);
      throw error;
    }
  }

  /**
   * Get price data for specific coins
   */
  async getSimplePrice(
    coinIds: string[],
    vsCurrencies: string[] = ["usd"],
    options: {
      includeMarketCap?: boolean;
      include24hrVol?: boolean;
      include24hrChange?: boolean;
      includeLastUpdatedAt?: boolean;
    } = {},
  ) {
    try {
      const params = new URLSearchParams({
        ids: coinIds.join(","),
        vs_currencies: vsCurrencies.join(","),
        include_market_cap: (options.includeMarketCap || false).toString(),
        include_24hr_vol: (options.include24hrVol || false).toString(),
        include_24hr_change: (options.include24hrChange || false).toString(),
        include_last_updated_at: (
          options.includeLastUpdatedAt || false
        ).toString(),
      });

      const response = await fetch(
        `${this.getBaseUrl()}/simple/price?${params}`,
        {
          headers: this.getHeaders(),
        },
      );

      if (!response.ok) {
        throw new Error(`CoinGecko API error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error fetching simple price:", error);
      throw error;
    }
  }

  /**
   * Convert search results to standardized token format
   */
  convertSearchToTokens(
    searchResult: CoinGeckoSearchResult,
  ): Partial<CoinGeckoToken>[] {
    return searchResult.coins.map((coin) => ({
      id: coin.id,
      symbol: coin.symbol,
      name: coin.name,
      image: coin.large,
      market_cap_rank: coin.market_cap_rank,
      // Other fields will be filled when fetching detailed data
      current_price: 0,
      market_cap: 0,
      price_change_percentage_24h: 0,
      total_volume: 0,
      circulating_supply: 0,
      total_supply: 0,
      max_supply: 0,
      ath: 0,
      atl: 0,
      last_updated: new Date().toISOString(),
    }));
  }

  /**
   * Get rate limit info (for Pro API)
   */
  async getRateLimitStatus() {
    if (!this.apiKey) {
      throw new Error("API key required for rate limit status");
    }

    try {
      const response = await fetch(`${this.proBaseUrl}/key`, {
        headers: this.getHeaders(),
      });

      if (!response.ok) {
        throw new Error(`CoinGecko API error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error fetching rate limit status:", error);
      throw error;
    }
  }
}
