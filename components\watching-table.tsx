"use client";

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ArrowUpDown, Eye, Play } from "lucide-react";
import { motion } from "framer-motion";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { WatchingItem } from "@/lib/utils";
import { useBots } from "@/contexts/bot-context";
import { formatUSD } from "@/lib/utils";
import { toast } from "sonner";

export function WatchingTable() {
  const { bots, startBot } = useBots();
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});

  // Convert inactive bots to watching items
  const watchingItems: WatchingItem[] = React.useMemo(() => {
    return bots
      .filter((bot) => !bot.isActive)
      .map((bot) => {
        // Generate mock indicator data
        const rsi = Math.random() * 100;
        const macdSignal = rsi > 70 ? "SELL" : rsi < 30 ? "BUY" : "HOLD";

        return {
          id: bot.id,
          botId: bot.id,
          botName: bot.name,
          token: "SOL", // Default token for demo
          tokenName: "Solana",
          tokenImage: `/api/placeholder/32/32`,
          currentPrice: 145.67 + (Math.random() - 0.5) * 10, // Mock price
          targetPrice: bot.config.strategy === "RSI" ? 150 : undefined,
          indicators: {
            rsi: rsi,
            macd:
              macdSignal === "BUY"
                ? "Bullish"
                : macdSignal === "SELL"
                  ? "Bearish"
                  : "Neutral",
            signal: macdSignal as "BUY" | "SELL" | "HOLD",
          },
          addedAt: new Date(Date.now() - Math.random() * 86400000 * 7), // Random date within last week
          lastChecked: new Date(Date.now() - Math.random() * 300000), // Random time within last 5 minutes
          strategy: bot.config.strategy,
        };
      });
  }, [bots]);

  const handleStartTrading = async (item: WatchingItem) => {
    try {
      await startBot(item.botId, 1); // Start with 1 SOL
      toast.success(`Started trading ${item.token} with ${item.botName}`);
    } catch {
      toast.error(`Failed to start trading ${item.token}`);
    }
  };

  const formatTime = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) {
      return "Just now";
    } else if (diffMins < 60) {
      return `${diffMins}m ago`;
    } else {
      const diffHours = Math.floor(diffMins / 60);
      return `${diffHours}h ago`;
    }
  };

  const getSignalColor = (signal: string) => {
    switch (signal) {
      case "BUY":
        return "text-green-500";
      case "SELL":
        return "text-red-500";
      default:
        return "text-yellow-500";
    }
  };

  const getSignalBadgeVariant = (signal: string) => {
    switch (signal) {
      case "BUY":
        return "default";
      case "SELL":
        return "destructive";
      default:
        return "secondary";
    }
  };

  const columns: ColumnDef<WatchingItem>[] = [
    {
      accessorKey: "token",
      header: "Token",
      cell: ({ row }) => {
        const item = row.original;
        return (
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-full bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center text-white text-sm font-bold">
              {item.token.slice(0, 2).toUpperCase()}
            </div>
            <div>
              <div className="font-medium">{item.token}</div>
              <div className="text-sm text-muted-foreground">
                {item.tokenName}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "botName",
      header: "Bot",
      cell: ({ row }) => {
        const item = row.original;
        return (
          <div>
            <Badge variant="outline" className="font-mono">
              {item.botName}
            </Badge>
            <div className="text-xs text-muted-foreground mt-1">
              {item.strategy}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "currentPrice",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Current Price
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const item = row.original;
        return (
          <div>
            <div className="font-mono">{formatUSD(item.currentPrice)}</div>
            {item.targetPrice && (
              <div className="text-xs text-muted-foreground">
                Target: {formatUSD(item.targetPrice)}
              </div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "indicators",
      header: "Indicators",
      cell: ({ row }) => {
        const item = row.original;
        return (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-xs text-muted-foreground">RSI:</span>
              <div className="flex items-center gap-1">
                <Progress value={item.indicators.rsi} className="w-12 h-2" />
                <span className="text-xs font-mono">
                  {item.indicators.rsi?.toFixed(0)}
                </span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs text-muted-foreground">MACD:</span>
              <span className="text-xs">{item.indicators.macd}</span>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "signal",
      header: "Signal",
      cell: ({ row }) => {
        const item = row.original;
        return (
          <Badge
            variant={getSignalBadgeVariant(item.indicators.signal || "HOLD")}
            className={`${getSignalColor(
              item.indicators.signal || "HOLD",
            )} font-mono`}
          >
            {item.indicators.signal || "HOLD"}
          </Badge>
        );
      },
    },
    {
      accessorKey: "lastChecked",
      header: "Last Check",
      cell: ({ row }) => {
        const item = row.original;
        return (
          <div className="text-sm text-muted-foreground">
            {formatTime(item.lastChecked)}
          </div>
        );
      },
    },
    {
      accessorKey: "addedAt",
      header: "Added",
      cell: ({ row }) => {
        const item = row.original;
        return (
          <div className="text-sm text-muted-foreground">
            {item.addedAt.toLocaleDateString()}
          </div>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const item = row.original;
        return (
          <Button
            size="sm"
            onClick={() => handleStartTrading(item)}
            className="flex items-center gap-1 bg-primary-gradient text-white"
          >
            <Play className="h-3 w-3" />
            Start Trading
          </Button>
        );
      },
    },
  ];

  const table = useReactTable({
    data: watchingItems,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Eye className="h-5 w-5" />
          Watching List
          <Badge variant="secondary">{watchingItems.length}</Badge>
          <div className="flex items-center gap-1 ml-auto">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-xs text-muted-foreground">Monitoring</span>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center py-4">
          <Input
            placeholder="Filter tokens..."
            value={(table.getColumn("token")?.getFilterValue() as string) ?? ""}
            onChange={(event) =>
              table.getColumn("token")?.setFilterValue(event.target.value)
            }
            className="max-w-sm"
          />
        </div>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row, index) => (
                  <motion.tr
                    key={row.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors"
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                  </motion.tr>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center"
                  >
                    No tokens being watched.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
