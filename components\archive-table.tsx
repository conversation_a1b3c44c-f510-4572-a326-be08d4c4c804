"use client";

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ArrowUpDown, Archive, Clock } from "lucide-react";
import { motion } from "framer-motion";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArchivedTrade } from "@/lib/utils";
import { useBots } from "@/contexts/bot-context";
import { formatSOL, formatUSD } from "@/lib/utils";

export function ArchiveTable() {
  const { trades, bots } = useBots();
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});

  // Convert completed trades to archived trades
  const archivedTrades: ArchivedTrade[] = React.useMemo(() => {
    return trades
      .filter((trade) => trade.status === "COMPLETED" && trade.type === "SELL")
      .map((trade) => {
        const bot = bots.find((b) => b.id === trade.botId);
        const entryTrade = trades.find(
          (t) =>
            t.botId === trade.botId &&
            t.token === trade.token &&
            t.type === "BUY" &&
            t.timestamp < trade.timestamp,
        );

        const entryPrice = entryTrade?.price || trade.price * 0.9; // Fallback
        const pnl =
          trade.pnl || ((trade.price - entryPrice) / entryPrice) * trade.amount;
        const pnlPercentage = ((trade.price - entryPrice) / entryPrice) * 100;
        const duration = entryTrade
          ? Math.floor(
              (trade.timestamp.getTime() - entryTrade.timestamp.getTime()) /
                60000,
            )
          : Math.floor(Math.random() * 1440); // Random duration in minutes

        return {
          id: trade.id,
          botId: trade.botId,
          botName: bot?.name || "Unknown Bot",
          token: trade.token,
          tokenName: trade.token,
          tokenImage: `/api/placeholder/32/32`,
          entryPrice,
          exitPrice: trade.price,
          amount: trade.amount,
          pnl,
          pnlPercentage,
          entryTime:
            entryTrade?.timestamp ||
            new Date(trade.timestamp.getTime() - duration * 60000),
          exitTime: trade.timestamp,
          duration,
          reason:
            Math.random() > 0.7
              ? "MANUAL"
              : Math.random() > 0.5
              ? "TAKE_PROFIT"
              : Math.random() > 0.3
              ? "STOP_LOSS"
              : "BOT_STOPPED",
        } as ArchivedTrade;
      });
  }, [trades, bots]);

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}m`;
    } else if (minutes < 1440) {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return `${hours}h ${mins}m`;
    } else {
      const days = Math.floor(minutes / 1440);
      const hours = Math.floor((minutes % 1440) / 60);
      return `${days}d ${hours}h`;
    }
  };

  const getReasonBadgeVariant = (reason: string) => {
    switch (reason) {
      case "TAKE_PROFIT":
        return "default";
      case "STOP_LOSS":
        return "destructive";
      case "MANUAL":
        return "secondary";
      default:
        return "outline";
    }
  };

  const columns: ColumnDef<ArchivedTrade>[] = [
    {
      accessorKey: "token",
      header: "Token",
      cell: ({ row }) => {
        const trade = row.original;
        return (
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-full bg-gradient-to-r from-gray-500 to-gray-700 flex items-center justify-center text-white text-sm font-bold">
              {trade.token.slice(0, 2).toUpperCase()}
            </div>
            <div>
              <div className="font-medium">{trade.token}</div>
              <div className="text-sm text-muted-foreground">
                {trade.tokenName}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "botName",
      header: "Bot",
      cell: ({ row }) => {
        const trade = row.original;
        return (
          <Badge variant="outline" className="font-mono">
            {trade.botName}
          </Badge>
        );
      },
    },
    {
      accessorKey: "entryPrice",
      header: "Entry",
      cell: ({ row }) => {
        const trade = row.original;
        return (
          <div className="font-mono text-sm">{formatUSD(trade.entryPrice)}</div>
        );
      },
    },
    {
      accessorKey: "exitPrice",
      header: "Exit",
      cell: ({ row }) => {
        const trade = row.original;
        return (
          <div className="font-mono text-sm">{formatUSD(trade.exitPrice)}</div>
        );
      },
    },
    {
      accessorKey: "pnl",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() =>
              column.toggleSorting(column.getIsSorted() === "asc")
            }>
            P&L
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const trade = row.original;
        return (
          <div
            className={`font-mono ${
              trade.pnl >= 0 ? "text-green-500" : "text-red-500"
            }`}>
            <div>{formatSOL(trade.pnl)}</div>
            <div className="text-xs">
              {trade.pnlPercentage >= 0 ? "+" : ""}
              {trade.pnlPercentage.toFixed(2)}%
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "duration",
      header: "Duration",
      cell: ({ row }) => {
        const trade = row.original;
        return (
          <div className="flex items-center gap-1 text-sm text-muted-foreground">
            <Clock className="h-3 w-3" />
            {formatDuration(trade.duration)}
          </div>
        );
      },
    },
    {
      accessorKey: "reason",
      header: "Exit Reason",
      cell: ({ row }) => {
        const trade = row.original;
        return (
          <Badge
            variant={getReasonBadgeVariant(trade.reason)}
            className="text-xs">
            {trade.reason.replace("_", " ")}
          </Badge>
        );
      },
    },
    {
      accessorKey: "exitTime",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() =>
              column.toggleSorting(column.getIsSorted() === "asc")
            }>
            Exit Time
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const trade = row.original;
        return (
          <div className="text-sm text-muted-foreground">
            <div>{trade.exitTime.toLocaleDateString()}</div>
            <div>
              {trade.exitTime.toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              })}
            </div>
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data: archivedTrades,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
    initialState: {
      sorting: [{ id: "exitTime", desc: true }],
    },
  });

  // Calculate summary stats
  const totalPnL = archivedTrades.reduce((sum, trade) => sum + trade.pnl, 0);
  const winningTrades = archivedTrades.filter((trade) => trade.pnl > 0).length;
  const winRate =
    archivedTrades.length > 0
      ? (winningTrades / archivedTrades.length) * 100
      : 0;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Archive className="h-5 w-5" />
          Trading Archive
          <Badge variant="secondary">{archivedTrades.length}</Badge>
        </CardTitle>
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <span>Total P&L:</span>
            <span
              className={`font-mono ${
                totalPnL >= 0 ? "text-green-500" : "text-red-500"
              }`}>
              {formatSOL(totalPnL)}
            </span>
          </div>
          <div className="flex items-center gap-1">
            <span>Win Rate:</span>
            <span className="font-mono">{winRate.toFixed(1)}%</span>
          </div>
          <div className="flex items-center gap-1">
            <span>Winning Trades:</span>
            <span className="font-mono">
              {winningTrades}/{archivedTrades.length}
            </span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center py-4">
          <Input
            placeholder="Filter tokens..."
            value={(table.getColumn("token")?.getFilterValue() as string) ?? ""}
            onChange={(event) =>
              table.getColumn("token")?.setFilterValue(event.target.value)
            }
            className="max-w-sm"
          />
        </div>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id}>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row, index) => (
                  <motion.tr
                    key={row.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className="hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors">
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                  </motion.tr>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-24 text-center">
                    No archived trades found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
