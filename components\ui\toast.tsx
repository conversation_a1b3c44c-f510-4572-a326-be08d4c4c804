'use client';

import { useEffect, useState } from 'react';

type ToastType = 'success' | 'error' | 'info' | 'warning';

interface ToastProps {
  message: string;
  type: ToastType;
  onClose: () => void;
  duration?: number;
}

export const Toast = ({ message, type, onClose, duration = 3000 }: ToastProps) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(onClose, 300); // Wait for fade out animation
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  const typeStyles = {
    success: 'bg-green-500',
    error: 'bg-red-500',
    info: 'bg-blue-500',
    warning: 'bg-yellow-500',
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div 
        className={`${typeStyles[type]} text-white px-4 py-2 rounded-md shadow-lg transition-opacity duration-300`}
        style={{ opacity: isVisible ? 1 : 0 }}
      >
        {message}
      </div>
    </div>
  );
};

// Toast hook
export const useToast = () => {
  const [toast, setToast] = useState<{
    message: string;
    type: ToastType;
    show: boolean;
  } | null>(null);

  const showToast = (message: string, type: ToastType = 'info') => {
    setToast({ message, type, show: true });
  };

  const hideToast = () => {
    setToast(null);
  };

  const ToastComponent = () => {
    if (!toast) return null;
    return (
      <Toast
        message={toast.message}
        type={toast.type}
        onClose={hideToast}
      />
    );
  };

  return { showToast, ToastComponent };
};
