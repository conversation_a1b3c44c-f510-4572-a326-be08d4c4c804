"use client";

import React, { useEffect, useRef, memo } from "react";

declare global {
  interface Window {
    TradingView: {
      widget: new (config: Record<string, unknown>) => void;
    };
  }
}

interface TradingViewChartProps {
  symbol: string;
  interval?: string;
  theme?: "light" | "dark";
  autosize?: boolean;
  height?: number;
  width?: number;
}

export const TradingViewChart = memo(function TradingViewChart({
  symbol,
  interval = "15",
  theme = "dark",
  autosize = true,
  height = 500,
  width = 800,
}: TradingViewChartProps) {
  const container = useRef<HTMLDivElement>(null);
  const scriptRef = useRef<HTMLScriptElement | null>(null);

  useEffect(() => {
    // Clean up previous script if it exists
    if (scriptRef.current) {
      scriptRef.current.remove();
    }

    // Create and load TradingView script
    const script = document.createElement("script");
    script.src =
      "https://s3.tradingview.com/external-embedding/embed-widget-advanced-chart.js";
    script.type = "text/javascript";
    script.async = true;

    // TradingView widget configuration
    const config = {
      autosize: autosize,
      width: autosize ? undefined : width,
      height: autosize ? undefined : height,
      symbol: `BINANCE:${symbol.toUpperCase()}USDT`, // Default to Binance pair
      interval: interval,
      timezone: "Etc/UTC",
      theme: theme,
      style: "1",
      locale: "en",
      toolbar_bg: "#f1f3f6",
      enable_publishing: false,
      allow_symbol_change: true,
      container_id: "tradingview_chart",
      studies: [
        "Volume@tv-basicstudies",
        "RSI@tv-basicstudies",
        "MACD@tv-basicstudies",
      ],
      show_popup_button: true,
      popup_width: "1000",
      popup_height: "650",
      no_referral_id: true,
      withdateranges: true,
      hide_side_toolbar: false,
      details: true,
      hotlist: true,
      calendar: true,
      news: ["headlines"],
      studies_overrides: {},
      overrides: {
        "paneProperties.background": theme === "dark" ? "#1a1a1a" : "#ffffff",
        "paneProperties.vertGridProperties.color":
          theme === "dark" ? "#2a2a2a" : "#e1e1e1",
        "paneProperties.horzGridProperties.color":
          theme === "dark" ? "#2a2a2a" : "#e1e1e1",
        "symbolWatermarkProperties.transparency": 90,
        "scalesProperties.textColor": theme === "dark" ? "#ffffff" : "#000000",
        "mainSeriesProperties.candleStyle.wickUpColor": "#26a69a",
        "mainSeriesProperties.candleStyle.wickDownColor": "#ef5350",
        "mainSeriesProperties.candleStyle.upColor": "#26a69a",
        "mainSeriesProperties.candleStyle.downColor": "#ef5350",
        "mainSeriesProperties.candleStyle.borderUpColor": "#26a69a",
        "mainSeriesProperties.candleStyle.borderDownColor": "#ef5350",
      },
    };

    script.innerHTML = JSON.stringify(config);

    if (container.current) {
      container.current.appendChild(script);
      scriptRef.current = script;
    }

    return () => {
      if (scriptRef.current) {
        scriptRef.current.remove();
        scriptRef.current = null;
      }
    };
  }, [symbol, interval, theme, autosize, height, width]);

  return (
    <div className="tradingview-widget-container w-full h-full">
      <div
        id="tradingview_chart"
        ref={container}
        className="tradingview-widget w-full h-full"
        style={{ height: autosize ? "100%" : `${height}px` }}
      />
      <div className="tradingview-widget-copyright">
        <a
          href={`https://www.tradingview.com/symbols/${symbol.toUpperCase()}USDT/`}
          rel="noopener nofollow"
          target="_blank"
          className="text-xs text-muted-foreground hover:text-primary"
        >
          <span className="blue-text">{symbol.toUpperCase()}USDT</span> by
          TradingView
        </a>
      </div>
    </div>
  );
});

// Alternative lightweight chart component for better performance
export const TradingViewMiniChart = memo(function TradingViewMiniChart({
  symbol,
  theme = "dark",
}: {
  symbol: string;
  theme?: "light" | "dark";
}) {
  const container = useRef<HTMLDivElement>(null);
  const scriptRef = useRef<HTMLScriptElement | null>(null);

  useEffect(() => {
    // Clean up previous script if it exists
    if (scriptRef.current) {
      scriptRef.current.remove();
    }

    // Create and load TradingView mini chart script
    const script = document.createElement("script");
    script.src =
      "https://s3.tradingview.com/external-embedding/embed-widget-mini-symbol-overview.js";
    script.type = "text/javascript";
    script.async = true;

    const config = {
      symbol: `BINANCE:${symbol.toUpperCase()}USDT`,
      width: "100%",
      height: "100%",
      locale: "en",
      dateRange: "12M",
      colorTheme: theme,
      trendLineColor: "rgba(41, 98, 255, 1)",
      underLineColor: "rgba(41, 98, 255, 0.3)",
      underLineBottomColor: "rgba(41, 98, 255, 0)",
      isTransparent: false,
      autosize: true,
      largeChartUrl: "",
    };

    script.innerHTML = JSON.stringify(config);

    if (container.current) {
      container.current.appendChild(script);
      scriptRef.current = script;
    }

    return () => {
      if (scriptRef.current) {
        scriptRef.current.remove();
        scriptRef.current = null;
      }
    };
  }, [symbol, theme]);

  return (
    <div className="tradingview-widget-container w-full h-full">
      <div ref={container} className="tradingview-widget w-full h-full" />
    </div>
  );
});
