"use client";

import * as React from "react";
import {
  ArrowUpDown,
  AudioWaveform,
  Bot,
  CircleDot,
  Command,
  CreditCard,
  GalleryVerticalEnd,
  Search,

  Star,
} from "lucide-react";

import { NavMain } from "@/components/sidebar/nav-main";
import { NavUser } from "@/components/sidebar/nav-user";
import { NavCoin } from "@/components/sidebar/nav-coin";
import { WalletSwitcher } from "@/components/sidebar/wallet-switcher";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarSeparator,
} from "@/components/ui/sidebar";
import { NavSearch } from "@/components/sidebar/nav-search";

// This is sample data.
const data = {
  user: {
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    avatar: "/logo.png",
  },
  wallet: [
    {
      name: "Acme Inc",
      logo: GalleryVerticalEnd,
      plan: "P1: 1000 SOL",
    },
    {
      name: "Acme Corp.",
      logo: AudioWaveform,
      plan: "P2: 10000 SOL",
    },
    {
      name: "Evil Corp.",
      logo: Command,
      plan: "P3: 5000 SOL",
    },
  ],
  search: [
    {
      title: "SEARCH",
      url: "/search",
      icon: Search,
    },
  ],
  navMain: [
    {
      title: "COIN",
      url: "/coins",
      icon: CircleDot,
    },
    {
      title: "BOT",
      url: "/bot",
      icon: Bot,
    },
    {
      title: "AI",
      url: "/ai",
      icon: Star,
    },
    {
      title: "TRADE",
      url: "/trades",
      icon: ArrowUpDown,
    },
    {
      title: "WALLET",
      url: "/wallet",
      icon: CreditCard,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <WalletSwitcher wallet={data.wallet} />
      </SidebarHeader>
      <SidebarContent>
        <SidebarSeparator />
        <NavSearch items={data.search} />
        <SidebarSeparator />
        <NavMain items={data.navMain} />
        <SidebarSeparator />
        <NavCoin />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
