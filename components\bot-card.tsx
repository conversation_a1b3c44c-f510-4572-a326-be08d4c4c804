"use client";

import { useState } from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import {
  Bot,
  TrendingUp,
  TrendingDown,
  Play,
  Pause,
  Settings,
} from "lucide-react";
import { toast } from "sonner";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Bot as BotType } from "@/lib/utils";
import { formatSOL, formatPercentage } from "@/lib/utils";

interface BotCardProps {
  bot: BotType;
  onStartBot: (botId: string, solPerTrade: number) => void;
  onStopBot: (botId: string) => void;
}

export function BotCard({ bot, onStartBot, onStopBot }: BotCardProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleToggleBot = async () => {
    setIsLoading(true);

    const toastId = toast.loading(
      bot.isActive ? "Stopping bot..." : "Starting bot...",
    );

    try {
      if (bot.isActive) {
        await onStopBot(bot.id);
        toast.success(`${bot.name} stopped successfully!`, { id: toastId });
      } else {
        await onStartBot(bot.id, bot.solPerTrade);
        toast.success(
          `${bot.name} started with ${formatSOL(bot.solPerTrade)} per trade!`,
          { id: toastId },
        );
      }
    } catch {
      toast.error("Failed to toggle bot. Please try again.", { id: toastId });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <motion.div
      whileHover={{ y: -2 }}
      transition={{ duration: 0.2 }}
      className="group"
    >
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3">
              <div
                className={`p-2 rounded-md ${bot.isActive ? "bg-green-500/10" : "bg-muted"}`}
              >
                <Bot
                  className={`h-4 w-4 ${bot.isActive ? "text-green-500" : "text-muted-foreground"}`}
                />
              </div>
              <div>
                <CardTitle className="text-sm font-medium dark:text-white">
                  {bot.name}
                </CardTitle>
                {/* <p className="text-xs text-muted-foreground mt-1 ">{bot.description}</p> */}
              </div>
            </div>
            <Badge
              variant={bot.isActive ? "default" : "secondary"}
              className={`text-xs ${bot.isActive ? "bg-green-500/10 text-green-500 border-green-500/20" : ""}`}
            >
              {bot.isActive && (
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1.5 animate-pulse" />
              )}
              {bot.isActive ? "Active" : "Inactive"}
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          {/* Stats Row */}
          <div className="flex items-center justify-between text-sm mb-4">
            <div className="flex items-center space-x-4">
              <div>
                <span className="text-muted-foreground">Trades:</span>
                <span className="ml-1 font-medium dark:text-white">
                  {bot.stats.totalTrades}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">Win Rate:</span>
                <span className="ml-1 font-medium dark:text-white">
                  {formatPercentage(bot.stats.winRate)}
                </span>
              </div>
            </div>
            <div
              className={`flex items-center font-medium ${
                bot.stats.pnl >= 0 ? "text-green-500" : "text-red-500"
              }`}
            >
              {bot.stats.pnl >= 0 ? (
                <TrendingUp className="h-3 w-3 mr-1" />
              ) : (
                <TrendingDown className="h-3 w-3 mr-1" />
              )}
              {formatSOL(Math.abs(bot.stats.pnl))}
            </div>
          </div>

          {/* SOL per Trade */}
          <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
            <span>SOL per Trade</span>
            <span className="font-bold dark:text-white">
              {formatSOL(bot.solPerTrade)}
            </span>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2 mb-4">
            <Button
              onClick={handleToggleBot}
              disabled={isLoading}
              size="sm"
              className={`flex-1 h-8 text-xs ${
                bot.isActive
                  ? "bg-red-500 hover:bg-red-600 text-white"
                  : "bg-green-500 hover:bg-green-600 text-white"
              }`}
            >
              {isLoading ? (
                <div className="h-3 w-3 border border-white border-t-transparent rounded-full animate-spin" />
              ) : bot.isActive ? (
                <>
                  <Pause className="h-3 w-3 mr-1" />
                  Stop
                </>
              ) : (
                <>
                  <Play className="h-3 w-3 mr-1" />
                  Start
                </>
              )}
            </Button>

            <Button asChild variant="outline" size="sm" className="h-8 px-3">
              <Link href={`/bot/${bot.id}`}>
                <Settings className="h-3 w-3" />
              </Link>
            </Button>
          </div>

          {/* Current Position */}
          {bot.stats.currentPosition && (
            <div className="flex items-center justify-between border-t border-gray-700 pt-4 mt-4">
              <span className="text-xs text-muted-foreground">
                Position: {bot.stats.currentPosition.token}
              </span>
              <span
                className={`text-xs font-medium ${
                  bot.stats.currentPosition.pnl >= 0
                    ? "text-green-500"
                    : "text-red-500"
                }`}
              >
                {formatSOL(bot.stats.currentPosition.pnl)}
              </span>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
