"use client";

import { motion } from "framer-motion";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
} from "recharts";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  TrendingUp,
  TrendingDown,
  Target,
  Zap,
  Calendar,
  DollarSign,
} from "lucide-react";
import { Bot, Trade } from "@/lib/utils";
import { formatSOL, formatPercentage } from "@/lib/utils";

interface BotAnalyticsProps {
  bot: Bot;
  trades: Trade[];
}

export function BotAnalytics({ bot, trades }: BotAnalyticsProps) {
  // Calculate performance metrics
  const completedTrades = trades.filter(
    (trade) => trade.status === "COMPLETED",
  );
  const winningTrades = completedTrades.filter((trade) => (trade.pnl || 0) > 0);
  const losingTrades = completedTrades.filter((trade) => (trade.pnl || 0) < 0);

  const totalPnL = completedTrades.reduce(
    (sum, trade) => sum + (trade.pnl || 0),
    0,
  );
  const avgWin =
    winningTrades.length > 0
      ? winningTrades.reduce((sum, trade) => sum + (trade.pnl || 0), 0) /
        winningTrades.length
      : 0;
  const avgLoss =
    losingTrades.length > 0
      ? Math.abs(
          losingTrades.reduce((sum, trade) => sum + (trade.pnl || 0), 0) /
            losingTrades.length,
        )
      : 0;

  // Prepare chart data
  const dailyPnL = completedTrades.reduce(
    (acc, trade) => {
      const date = trade.timestamp.toDateString();
      if (!acc[date]) {
        acc[date] = { date: date.slice(4, 10), pnl: 0, trades: 0 };
      }
      acc[date].pnl += trade.pnl || 0;
      acc[date].trades += 1;
      return acc;
    },
    {} as Record<string, { date: string; pnl: number; trades: number }>,
  );

  const chartData = Object.values(dailyPnL).slice(-7); // Last 7 days

  const pieData = [
    { name: "Winning Trades", value: winningTrades.length, color: "#10b981" },
    { name: "Losing Trades", value: losingTrades.length, color: "#ef4444" },
  ];

  const metrics = [
    {
      title: "Total P&L",
      value: formatSOL(totalPnL),
      icon: DollarSign,
      color: totalPnL >= 0 ? "text-green-400" : "text-red-400",
      bgColor: totalPnL >= 0 ? "bg-green-500/10" : "bg-red-500/10",
    },
    {
      title: "Win Rate",
      value: formatPercentage(bot.stats.winRate),
      icon: Target,
      color: "text-blue-400",
      bgColor: "bg-blue-500/10",
    },
    {
      title: "Avg Win",
      value: formatSOL(avgWin),
      icon: TrendingUp,
      color: "text-green-400",
      bgColor: "bg-green-500/10",
    },
    {
      title: "Avg Loss",
      value: formatSOL(avgLoss),
      icon: TrendingDown,
      color: "text-red-400",
      bgColor: "bg-red-500/10",
    },
  ];

  return (
    <div className="space-y-6">
      {/* Performance Metrics */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric, index) => (
          <motion.div
            key={metric.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card>
              <CardContent className="p-4">
                <div className={`p-2 rounded-lg ${metric.bgColor} w-fit mb-2`}>
                  <metric.icon className={`h-4 w-4 ${metric.color}`} />
                </div>
                <div className={`text-lg font-bold ${metric.color}`}>
                  {metric.value}
                </div>
                <div className="text-xs text-gray-400">{metric.title}</div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Daily P&L Chart */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Daily Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis
                      dataKey="date"
                      stroke="#9ca3af"
                      fontSize={12}
                      tickLine={false}
                      axisLine={false}
                    />
                    <YAxis
                      stroke="#9ca3af"
                      fontSize={12}
                      tickLine={false}
                      axisLine={false}
                      tickFormatter={(value) => `${value.toFixed(2)}`}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: "#1f2937",
                        border: "1px solid #374151",
                        borderRadius: "0.5rem",
                        color: "#f9fafb",
                      }}
                      formatter={(value: number) => [formatSOL(value), "P&L"]}
                    />
                    <Bar dataKey="pnl" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Win/Loss Distribution */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card className="">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Trade Distribution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center">
                {pieData.some((d) => d.value > 0) ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={pieData}
                        cx="50%"
                        cy="50%"
                        innerRadius={60}
                        outerRadius={100}
                        paddingAngle={5}
                        dataKey="value"
                      >
                        {pieData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip
                        contentStyle={{
                          backgroundColor: "#1f2937",
                          border: "1px solid #374151",
                          borderRadius: "0.5rem",
                          color: "#f9fafb",
                        }}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="text-center text-gray-400">
                    <div className="text-lg mb-2">No trade data</div>
                    <div className="text-sm">
                      Start trading to see analytics
                    </div>
                  </div>
                )}
              </div>

              {pieData.some((d) => d.value > 0) && (
                <div className="flex justify-center gap-4 mt-4">
                  {pieData.map((entry) => (
                    <div key={entry.name} className="flex items-center gap-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: entry.color }}
                      />
                      <span className="text-sm text-gray-300">
                        {entry.name}: {entry.value}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Strategy Performance */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
      >
        <Card className="">
          <CardHeader>
            <CardTitle className="text-white">Strategy Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <div className="text-sm text-gray-400">Strategy Type</div>
                <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                  {bot.config.strategy}
                </Badge>
              </div>

              <div className="space-y-2">
                <div className="text-sm text-gray-400">Timeframe</div>
                <div className="text-white font-medium">
                  {bot.config.timeframe}
                </div>
              </div>

              <div className="space-y-2">
                <div className="text-sm text-gray-400">Risk Management</div>
                <div className="text-white font-medium">
                  SL: {bot.config.stopLoss}% | TP: {bot.config.takeProfit}%
                </div>
              </div>

              <div className="space-y-2">
                <div className="text-sm text-gray-400">Max Slippage</div>
                <div className="text-white font-medium">
                  {bot.config.maxSlippage}%
                </div>
              </div>

              <div className="space-y-2">
                <div className="text-sm text-gray-400">Min Liquidity</div>
                <div className="text-white font-medium">
                  {formatSOL(bot.config.minLiquidity)}
                </div>
              </div>

              <div className="space-y-2">
                <div className="text-sm text-gray-400">Max Positions</div>
                <div className="text-white font-medium">
                  {bot.config.maxPositions}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
