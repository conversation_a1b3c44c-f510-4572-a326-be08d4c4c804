"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  Settings,
  TrendingUp,
  DollarSign,
  Activity,
  BarChart3,
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { NewCoinListing } from "@/lib/real-time-data";
import { useUser } from "@clerk/nextjs";
import { toast } from "sonner";
import Image from "next/image";

interface BotSetupModalProps {
  isOpen: boolean;
  onClose: () => void;
  coin: NewCoinListing | null;
}

export function BotSetupModal({ isOpen, onClose, coin }: BotSetupModalProps) {
  const { user } = useUser();
  const [buyAmount, setBuyAmount] = useState("100");
  const [sellTarget, setSellTarget] = useState("20");
  const [stopLoss, setStopLoss] = useState("10");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Trading strategy settings
  const [strategy, setStrategy] = useState("EMA_CROSSOVER");
  const [fastEMA, setFastEMA] = useState("12");
  const [slowEMA, setSlowEMA] = useState("26");
  const [useStopLoss] = useState(true);
  const [useTakeProfit] = useState(true);
  const [rsiEnabled] = useState(false);
  const [rsiPeriod] = useState("14");
  const [rsiOverbought] = useState("70");
  const [rsiOversold] = useState("30");

  const handleSetupBot = async () => {
    if (!coin || !user) {
      toast.error("Please sign in to create a bot");
      return;
    }

    setIsSubmitting(true);

    try {
      // Create bot configuration
      const botConfig = {
        targetCoin: coin.symbol,
        buyAmountUSD: parseFloat(buyAmount),
        sellTargetPercent: parseFloat(sellTarget),
        stopLossPercent: parseFloat(stopLoss),
        maxPositions: 1,
        riskLevel: "medium",
      };

      // Create the bot via API
      const response = await fetch("/api/bots", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: `${strategy.replace("_", " ")} - ${coin.symbol}`,
          description: `Automated ${strategy
            .replace("_", " ")
            .toLowerCase()} trading bot for ${coin.name}`,
          strategy,
          tradingMode: "COIN_TRADING",
          solPerTrade: parseFloat(buyAmount) / (coin.current_price || 100), // Convert USD to SOL estimate
          config: botConfig,
          fastEmaPeriod: strategy === "EMA_CROSSOVER" ? fastEMA : null,
          slowEmaPeriod: strategy === "EMA_CROSSOVER" ? slowEMA : null,
          rsiPeriod: rsiEnabled ? rsiPeriod : null,
          rsiOverbought: rsiEnabled ? rsiOverbought : null,
          rsiOversold: rsiEnabled ? rsiOversold : null,
          stopLossPercentage: useStopLoss ? parseFloat(stopLoss) : null,
          takeProfitPercentage: useTakeProfit ? parseFloat(sellTarget) : null,
          useStopLoss,
          useTakeProfit,
          selectedCoin: coin.symbol,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to create bot");
      }

      const { bot } = await response.json();

      console.log("✅ Bot created successfully:", bot);

      toast.success(
        `${strategy.replace("_", " ")} bot created for ${coin.symbol}!`,
        {
          description: `Bot ID: ${bot.id.slice(
            0,
            8,
          )}... | Strategy: ${strategy}`,
        },
      );

      onClose();
    } catch (error) {
      console.error("❌ Failed to create bot:", error);
      toast.error("Failed to create bot", {
        description:
          error instanceof Error ? error.message : "Unknown error occurred",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!coin) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5" />
            Set Up Trading Bot
          </DialogTitle>
          <DialogDescription>
            Configure automated trading parameters for {coin.name} (
            {coin.symbol})
          </DialogDescription>
        </DialogHeader>

        {/* Token Info */}
        <Card>
          <CardHeader>
            <div className="flex items-center gap-3">
              {coin.image_url && (
                <Image
                  src={coin.image_url}
                  alt={coin.name}
                  width={40}
                  height={40}
                  className="rounded-full"
                  onError={(e) => {
                    e.currentTarget.style.display = "none";
                  }}
                />
              )}
              <div>
                <CardTitle className="text-lg">{coin.name}</CardTitle>
                <CardDescription>{coin.symbol.toUpperCase()}</CardDescription>
              </div>
              <div className="ml-auto">
                <Badge variant="secondary">{coin.dex_id}</Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="text-muted-foreground">Current Price</div>
                <div className="font-medium">
                  $
                  {coin.current_price < 0.01
                    ? coin.current_price.toFixed(6)
                    : coin.current_price.toFixed(4)}
                </div>
              </div>
              <div>
                <div className="text-muted-foreground">24h Change</div>
                <div
                  className={`font-medium ${
                    coin.price_change_24h >= 0
                      ? "text-green-600"
                      : "text-red-600"
                  }`}
                >
                  {coin.price_change_24h >= 0 ? "+" : ""}
                  {coin.price_change_24h.toFixed(2)}%
                </div>
              </div>
              <div>
                <div className="text-muted-foreground">Volume (24h)</div>
                <div className="font-medium">
                  $
                  {coin.volume_24h >= 1e6
                    ? `${(coin.volume_24h / 1e6).toFixed(2)}M`
                    : `${(coin.volume_24h / 1e3).toFixed(2)}K`}
                </div>
              </div>
              <div>
                <div className="text-muted-foreground">Market Cap</div>
                <div className="font-medium">
                  $
                  {coin.market_cap >= 1e6
                    ? `${(coin.market_cap / 1e6).toFixed(2)}M`
                    : `${(coin.market_cap / 1e3).toFixed(2)}K`}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bot Configuration */}
        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="basic">Basic Setup</TabsTrigger>
            <TabsTrigger value="advanced">Advanced</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="grid gap-4">
              <div className="space-y-2">
                <Label htmlFor="buyAmount" className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  Buy Amount (USD)
                </Label>
                <Input
                  id="buyAmount"
                  type="number"
                  value={buyAmount}
                  onChange={(e) => setBuyAmount(e.target.value)}
                  placeholder="100"
                />
                <p className="text-xs text-muted-foreground">
                  Amount in USD to invest when buying this token
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="sellTarget" className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Sell Target (%)
                </Label>
                <Input
                  id="sellTarget"
                  type="number"
                  value={sellTarget}
                  onChange={(e) => setSellTarget(e.target.value)}
                  placeholder="20"
                />
                <p className="text-xs text-muted-foreground">
                  Profit percentage at which to sell the token
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="stopLoss" className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Stop Loss (%)
                </Label>
                <Input
                  id="stopLoss"
                  type="number"
                  value={stopLoss}
                  onChange={(e) => setStopLoss(e.target.value)}
                  placeholder="10"
                />
                <p className="text-xs text-muted-foreground">
                  Loss percentage at which to sell to limit losses
                </p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-6">
            {/* Trading Strategy Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Trading Strategy
                </CardTitle>
                <CardDescription>
                  Choose the trading strategy for your bot
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="strategy">Strategy Type</Label>
                  <Select value={strategy} onValueChange={setStrategy}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select trading strategy" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="EMA_CROSSOVER">
                        EMA Crossover
                      </SelectItem>
                      <SelectItem value="RSI_MEAN_REVERSION">
                        RSI Mean Reversion
                      </SelectItem>
                      <SelectItem value="MACD_MOMENTUM">
                        MACD Momentum
                      </SelectItem>
                      <SelectItem value="COMBINED_SIGNALS">
                        Combined Signals
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {strategy === "EMA_CROSSOVER" && (
                  <div className="space-y-4 p-4 border rounded-lg bg-muted/50">
                    <h4 className="font-medium flex items-center gap-2">
                      <Activity className="h-4 w-4" />
                      EMA Crossover Settings
                    </h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="fastEMA">Fast EMA Period</Label>
                        <Input
                          id="fastEMA"
                          type="number"
                          value={fastEMA}
                          onChange={(e) => setFastEMA(e.target.value)}
                          placeholder="12"
                        />
                        <p className="text-xs text-muted-foreground">
                          Shorter period for faster signals
                        </p>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="slowEMA">Slow EMA Period</Label>
                        <Input
                          id="slowEMA"
                          type="number"
                          value={slowEMA}
                          onChange={(e) => setSlowEMA(e.target.value)}
                          placeholder="26"
                        />
                        <p className="text-xs text-muted-foreground">
                          Longer period for trend confirmation
                        </p>
                      </div>
                    </div>
                    <div className="text-sm text-muted-foreground p-3 bg-background rounded border">
                      <strong>How it works:</strong> Buy when fast EMA crosses
                      above slow EMA (bullish crossover), sell when fast EMA
                      crosses below slow EMA (bearish crossover).
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4">
          <Button variant="outline" onClick={onClose} className="flex-1">
            Cancel
          </Button>
          <Button
            onClick={handleSetupBot}
            disabled={isSubmitting}
            className="flex-1"
          >
            {isSubmitting ? "Setting up..." : "Set Up Bot"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
