"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  TrendingUp,
  TrendingDown,

  RefreshCw,
} from "lucide-react";
import { DexScreenerToken } from "@/lib/dexscreener-service";

interface DexScreenerCoinListProps {
  chainId?: string;
  limit?: number;
  showHeader?: boolean;
}

export function DexScreenerCoinList({
  chainId = "solana",
  limit = 10,
  showHeader = true,
}: DexScreenerCoinListProps) {
  const [tokens, setTokens] = useState<DexScreenerToken[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTrendingTokens = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(
        `/api/proxy/dexscreener?trending=true&chain=${chainId}`,
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch trending tokens: ${response.status}`);
      }

      const data = await response.json();
      const limitedTokens = data.pairs?.slice(0, limit) || [];
      setTokens(limitedTokens);
    } catch (err) {
      console.error("Error fetching trending tokens:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch tokens");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTrendingTokens();
  }, [chainId, limit]);

  const formatPrice = (price: string | undefined) => {
    if (!price) return "$0.00";
    const num = parseFloat(price);
    if (num < 0.01) {
      return `$${num.toExponential(2)}`;
    }
    return `$${num.toFixed(6)}`;
  };

  const formatVolume = (volume: number) => {
    if (volume >= 1000000) {
      return `$${(volume / 1000000).toFixed(1)}M`;
    } else if (volume >= 1000) {
      return `$${(volume / 1000).toFixed(1)}K`;
    }
    return `$${volume.toFixed(0)}`;
  };



  const getTokenImageUrl = (token: DexScreenerToken) => {
    return (
      token.info?.imageUrl ||
      `https://api.dicebear.com/7.x/identicon/svg?seed=${token.baseToken.symbol}`
    );
  };

  if (loading) {
    return (
      <Card>
        {showHeader && (
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <RefreshCw className="h-5 w-5 animate-spin" />
              Loading Trending Tokens...
            </CardTitle>
          </CardHeader>
        )}
        <CardContent>
          <div className="space-y-3">
            {Array.from({ length: 5 }).map((_, i) => (
              <div
                key={i}
                className="flex items-center gap-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-800 animate-pulse"
              >
                <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24" />
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-16" />
                </div>
                <div className="text-right space-y-2">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20" />
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-16" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        {showHeader && (
          <CardHeader>
            <CardTitle className="text-red-500">Error Loading Tokens</CardTitle>
          </CardHeader>
        )}
        <CardContent>
          <p className="text-red-500 mb-4">{error}</p>
          <Button onClick={fetchTrendingTokens} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  const containerClass = showHeader ? "" : "bg-transparent border-0";

  return (
    <Card className={containerClass}>
      {showHeader && (
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Trending Tokens on DexScreener</CardTitle>
            <Button onClick={fetchTrendingTokens} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardHeader>
      )}
      <CardContent className={showHeader ? "" : "p-0"}>
        {!showHeader && (
          <div className="flex items-center justify-between mb-4">
            <Button
              onClick={fetchTrendingTokens}
              variant="ghost"
              size="sm"
              className="text-gray-400 hover:text-white p-0"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        )}
        {tokens.length === 0 ? (
          <p className="text-gray-400 text-center py-4 text-sm">
            No trending tokens found
          </p>
        ) : (
          <div className="space-y-2">
            {tokens.map((token) => (
              <div
                key={`${token.baseToken.address}-${token.chainId}`}
                className={`flex items-center gap-3 p-3 rounded-lg transition-colors cursor-pointer ${
                  showHeader
                    ? "border hover:bg-gray-50 dark:hover:bg-gray-800"
                    : "hover:bg-gray-800/50 border border-gray-800"
                }`}
                onClick={() =>
                  (window.location.href = `/coin/${token.baseToken.address}`)
                }
              >
                {/* Token Image and Info */}
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  <div className="relative w-8 h-8 flex-shrink-0">
                    <Image
                      src={getTokenImageUrl(token)}
                      alt={token.baseToken.name}
                      fill
                      className="rounded-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `https://api.dicebear.com/7.x/identicon/svg?seed=${token.baseToken.symbol}`;
                      }}
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium truncate text-sm">
                        {token.baseToken.name}
                      </h3>
                      <Badge
                        variant="secondary"
                        className={`text-xs ${!showHeader ? "bg-gray-800 text-gray-300" : ""}`}
                      >
                        {token.baseToken.symbol}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2 text-xs text-gray-400">
                      <span>{formatVolume(token.volume?.h24 || 0)}</span>
                    </div>
                  </div>
                </div>

                {/* Price and Change */}
                <div className="text-right flex-shrink-0">
                  <div className="font-medium text-sm">
                    {formatPrice(token.priceUsd)}
                  </div>
                  <div
                    className={`text-xs flex items-center gap-1 justify-end ${
                      (token.priceChange?.h24 || 0) >= 0
                        ? "text-green-400"
                        : "text-red-400"
                    }`}
                  >
                    {(token.priceChange?.h24 || 0) >= 0 ? (
                      <TrendingUp className="h-3 w-3" />
                    ) : (
                      <TrendingDown className="h-3 w-3" />
                    )}
                    {(token.priceChange?.h24 || 0).toFixed(1)}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
