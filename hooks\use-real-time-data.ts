import { useState, useEffect, useCallback } from "react";
import {
  realTimeDataService,
  CoinData,
  PriceUpdate,
  HistoricalPrice,
  NewCoinListing,
} from "@/lib/real-time-data";

// Hook for real-time price updates
export function useRealTimePrice(symbol: string) {
  const [price, setPrice] = useState<number | null>(null);
  const [change24h, setChange24h] = useState<number>(0);
  const [volume24h, setVolume24h] = useState<number>(0);
  const [lastUpdate, setLastUpdate] = useState<number>(0);
  const [isConnected, setIsConnected] = useState<boolean>(false);

  useEffect(() => {
    if (!symbol) return;

    setIsConnected(true);

    const unsubscribe = realTimeDataService.subscribeToPrice(
      symbol,
      (update: PriceUpdate) => {
        setPrice(update.price);
        setChange24h(update.change_24h);
        setVolume24h(update.volume_24h);
        setLastUpdate(update.timestamp);
      },
    );

    // Cleanup on unmount
    return () => {
      unsubscribe();
      setIsConnected(false);
    };
  }, [symbol]);

  return {
    price,
    change24h,
    volume24h,
    lastUpdate,
    isConnected,
  };
}

// Hook for multiple coin data
export function useCoinData(symbols: string[]) {
  const [data, setData] = useState<CoinData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [lastFetch, setLastFetch] = useState<number>(0);

  const fetchData = useCallback(async () => {
    if (symbols.length === 0) {
      setData([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const coinData = await realTimeDataService.getCoinData(symbols);
      setData(coinData);
      setLastFetch(Date.now());
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to fetch coin data",
      );
      console.error("Coin data fetch error:", err);
    } finally {
      setLoading(false);
    }
  }, [symbols]);

  useEffect(() => {
    fetchData();

    // Refresh data every 30 seconds
    const interval = setInterval(fetchData, 30000);

    return () => clearInterval(interval);
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    lastFetch,
    refetch: fetchData,
  };
}

// Hook for historical price data
export function useHistoricalPrices(symbol: string, days: number = 1) {
  const [data, setData] = useState<HistoricalPrice[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!symbol) {
      setData([]);
      setLoading(false);
      return;
    }

    const fetchHistoricalData = async () => {
      try {
        setLoading(true);
        setError(null);
        const historicalData = await realTimeDataService.getHistoricalPrices(
          symbol,
          days,
        );
        setData(historicalData);
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : "Failed to fetch historical data",
        );
        console.error("Historical data fetch error:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchHistoricalData();
  }, [symbol, days]);

  return {
    data,
    loading,
    error,
  };
}

// Hook for price chart data with real-time updates
export function usePriceChart(
  symbol: string,
  timeframe: "1h" | "24h" | "7d" = "1h",
) {
  const [chartData, setChartData] = useState<
    Array<{
      time: string;
      price: number;
      timestamp: number;
    }>
  >([]);
  const [currentPrice, setCurrentPrice] = useState<number | null>(null);
  const [priceChange, setPriceChange] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);

  // intervalRef not used in this hook

  // Convert timeframe to days for API
  const getDaysFromTimeframe = (tf: string) => {
    switch (tf) {
      case "1h":
        return 0.04; // ~1 hour
      case "24h":
        return 1;
      case "7d":
        return 7;
      default:
        return 1;
    }
  };

  // Fetch initial historical data
  useEffect(() => {
    if (!symbol) return;

    const fetchInitialData = async () => {
      try {
        setLoading(true);
        const days = getDaysFromTimeframe(timeframe);
        const historicalData = await realTimeDataService.getHistoricalPrices(
          symbol,
          days,
        );

        const formattedData = historicalData.map((item) => ({
          time: new Date(item.timestamp).toLocaleTimeString("en-US", {
            hour12: false,
            hour: "2-digit",
            minute: "2-digit",
          }),
          price: item.price,
          timestamp: item.timestamp,
        }));

        setChartData(formattedData);

        if (formattedData.length > 0) {
          const firstPrice = formattedData[0].price;
          const lastPrice = formattedData[formattedData.length - 1].price;
          setCurrentPrice(lastPrice);
          setPriceChange(((lastPrice - firstPrice) / firstPrice) * 100);
        }
      } catch (error) {
        console.error("Failed to fetch initial chart data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchInitialData();
  }, [symbol, timeframe]);

  // Subscribe to real-time updates
  useEffect(() => {
    if (!symbol) return;

    const unsubscribe = realTimeDataService.subscribeToPrice(
      symbol,
      (update: PriceUpdate) => {
        setCurrentPrice(update.price);

        setChartData((prev) => {
          if (prev.length === 0) return prev;

          const newDataPoint = {
            time: new Date(update.timestamp).toLocaleTimeString("en-US", {
              hour12: false,
              hour: "2-digit",
              minute: "2-digit",
            }),
            price: update.price,
            timestamp: update.timestamp,
          };

          // Keep only last 50 data points for performance
          const newData = [...prev.slice(-49), newDataPoint];

          // Update price change
          if (newData.length > 1) {
            const firstPrice = newData[0].price;
            const changePercent =
              ((update.price - firstPrice) / firstPrice) * 100;
            setPriceChange(changePercent);
          }

          return newData;
        });
      },
    );

    return unsubscribe;
  }, [symbol]);

  return {
    chartData,
    currentPrice,
    priceChange,
    loading,
  };
}

// Hook for coin search
export function useCoinSearch() {
  const [results, setResults] = useState<unknown[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const search = useCallback(async (query: string) => {
    if (!query.trim()) {
      setResults([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const searchResults = await realTimeDataService.searchCoins(query);
      setResults(searchResults);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Search failed");
      console.error("Coin search error:", err);
    } finally {
      setLoading(false);
    }
  }, []);

  const clearResults = useCallback(() => {
    setResults([]);
    setError(null);
  }, []);

  return {
    results,
    loading,
    error,
    search,
    clearResults,
  };
}

// Hook for managing real-time data service lifecycle
export function useRealTimeDataService() {
  useEffect(() => {
    // Service is automatically initialized
    return () => {
      // Cleanup on app unmount
      realTimeDataService.disconnect();
    };
  }, []);

  return realTimeDataService;
}

// Hook for price alerts
export function usePriceAlert(
  symbol: string,
  targetPrice: number,
  direction: "above" | "below",
) {
  const [triggered, setTriggered] = useState<boolean>(false);
  const [currentPrice, setCurrentPrice] = useState<number | null>(null);

  useEffect(() => {
    if (!symbol || !targetPrice) return;

    const unsubscribe = realTimeDataService.subscribeToPrice(
      symbol,
      (update: PriceUpdate) => {
        setCurrentPrice(update.price);

        if (!triggered) {
          const shouldTrigger =
            direction === "above"
              ? update.price >= targetPrice
              : update.price <= targetPrice;

          if (shouldTrigger) {
            setTriggered(true);
          }
        }
      },
    );

    return unsubscribe;
  }, [symbol, targetPrice, direction, triggered]);

  const resetAlert = useCallback(() => {
    setTriggered(false);
  }, []);

  return {
    triggered,
    currentPrice,
    resetAlert,
  };
}

// Hook for new coin listings with database integration
export function useNewCoinListings(
  chain: "solana" | "ethereum",
  source: "all" | "pumpfun" | "raydium" | "dexscreener" = "all",
  limit: number = 50,
  refreshInterval: number = 15000, // 15 seconds for more real-time updates
) {
  const [listings, setListings] = useState<NewCoinListing[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [lastFetch, setLastFetch] = useState<number>(0);

  const fetchListings = useCallback(
    async (forceRefresh = false) => {
      try {
        setLoading(true);
        setError(null);

        if (forceRefresh) {
          // Force sync from external APIs to database
          await fetch("/api/coins/sync-realtime", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              action: "sync_listings",
              chain,
              limit,
            }),
          });

          // Also sync trending tokens from CoinMarketCap
          await fetch("/api/coins/sync-realtime", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ action: "sync_trending" }),
          });
        }

        // Get coins from database with real-time updates
        const response = await fetch(
          `/api/coins/sync-realtime?source=database&limit=${limit}`,
        );

        if (!response.ok) {
          console.warn(
            `API returned ${response.status}, attempting fallback...`,
          );
          // Don't throw immediately, try to parse the response for fallback data
        }

        const data = await response.json();

        // Handle different response types (database, fallback, error)
        if (data.coins && Array.isArray(data.coins)) {
          setListings(data.coins);
          setLastFetch(Date.now());

          if (data.warning) {
            console.warn(`Data source warning: ${data.warning}`);
          }

          console.log(
            `Fetched ${data.coins.length} listings from ${
              data.source || "unknown source"
            }`,
          );
        } else if (data.error) {
          throw new Error(data.details || data.error);
        } else {
          throw new Error("Invalid response format");
        }
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to fetch new listings",
        );
        console.error("New listings fetch error:", err);

        // Fallback to real-time service
        try {
          const fallbackListings = await realTimeDataService.getNewListings(
            chain,
            source,
            limit,
          );
          setListings(fallbackListings);
          console.log(`Fallback: Fetched ${fallbackListings.length} listings`);
        } catch (fallbackErr) {
          console.error("Fallback fetch error:", fallbackErr);
        }
      } finally {
        setLoading(false);
      }
    },
    [chain, source, limit],
  );

  const forceRefresh = useCallback(() => {
    console.log("Force refreshing listings and syncing to database...");
    fetchListings(true);
  }, [fetchListings]);

  useEffect(() => {
    fetchListings();

    const interval = setInterval(fetchListings, refreshInterval);

    return () => clearInterval(interval);
  }, [fetchListings, refreshInterval]);

  return {
    listings,
    loading,
    error,
    lastFetch,
    refetch: () => fetchListings(false),
    forceRefresh,
  };
}

// Hook for trending coins
export function useTrendingCoins(
  chain: "solana" | "ethereum",
  refreshInterval: number = 60000,
) {
  const [trending, setTrending] = useState<NewCoinListing[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [lastFetch, setLastFetch] = useState<number>(0);

  const fetchTrending = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const trendingCoins = await realTimeDataService.getTrendingCoins(chain);
      setTrending(trendingCoins);
      setLastFetch(Date.now());
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to fetch trending coins",
      );
      console.error("Trending coins fetch error:", err);
    } finally {
      setLoading(false);
    }
  }, [chain]);

  useEffect(() => {
    fetchTrending();

    const interval = setInterval(fetchTrending, refreshInterval);

    return () => clearInterval(interval);
  }, [fetchTrending, refreshInterval]);

  return {
    trending,
    loading,
    error,
    lastFetch,
    refetch: fetchTrending,
  };
}

// Hook for pump.fun real-time trench data with database integration
export function usePumpTrenchData() {
  const [tokens, setTokens] = useState<NewCoinListing[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState<boolean>(false);

  useEffect(() => {
    setLoading(true);

    const fetchDatabaseTokens = async () => {
      try {
        // Get tokens from database with real-time updates
        const response = await fetch(
          "/api/coins/sync-realtime?source=database&limit=100",
        );
        if (response.ok) {
          const data = await response.json();
          setTokens(data.coins || []);
          setIsConnected(true);
          setLoading(false);
          console.log(`Loaded ${data.coins?.length || 0} tokens from database`);
          return;
        }
      } catch (err) {
        console.error("Failed to fetch from database:", err);
      }

      // Fallback to real-time service
      try {
        setIsConnected(realTimeDataService.isPumpConnected);

        // Subscribe to real-time pump.fun updates
        const unsubscribe = realTimeDataService.subscribeToPumpUpdates(
          (updatedTokens) => {
            setTokens(updatedTokens);
            setLoading(false);
            setError(null);
            setIsConnected(realTimeDataService.isPumpConnected);
          },
        );

        // Initial load from real-time service
        const initialTokens = realTimeDataService
          .getPumpTokensByStage("launched")
          .concat(realTimeDataService.getPumpTokensByStage("graduating"))
          .concat(realTimeDataService.getPumpTokensByStage("graduated"))
          .concat(realTimeDataService.getPumpTokensByStage("matured"));

        if (initialTokens.length > 0) {
          setTokens(initialTokens);
          setLoading(false);
        }

        return unsubscribe;
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to load tokens");
        setLoading(false);
      }
    };

    fetchDatabaseTokens();
  }, []);

  // Get tokens by stage
  const getTokensByStage = useCallback(
    (stage: "launched" | "graduating" | "graduated" | "matured") => {
      return tokens.filter((token) => token.pump_stage === stage);
    },
    [tokens],
  );

  return {
    tokens,
    loading,
    error,
    isConnected,
    getTokensByStage,
  };
}
